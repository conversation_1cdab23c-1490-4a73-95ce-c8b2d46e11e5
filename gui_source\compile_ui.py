#!/usr/bin/env python3
"""
UI Compilation Script for WPX-QT-GUI
Auto-compiles .ui files to .py files using PyQt5 tools
"""

import os
import sys
import subprocess
from pathlib import Path

# UI files and their corresponding output files
UI_FILES = {
    'mdp_gui_template/mainwindow.ui': 'mdp_gui_template/mainwindow_ui.py',
    'mdp_gui_template/settings.ui': 'mdp_gui_template/settings_ui.py',
    'mdp_gui_template/updates.ui': 'mdp_gui_template/updates_ui.py',
    'mdp_gui_template/graphics.ui': 'mdp_gui_template/graphics_ui.py',
}

def check_pyuic5():
    """Check if pyuic5 is available"""
    try:
        result = subprocess.run(['pyuic5', '--version'], capture_output=True, text=True)
        print(f"Found pyuic5: {result.stdout.strip()}")
        return True
    except FileNotFoundError:
        try:
            # Try alternative method with python module
            result = subprocess.run([sys.executable, '-m', 'PyQt5.uic.pyuic', '--help'], 
                                  capture_output=True, text=True)
            print("Found PyQt5.uic.pyuic module")
            return True
        except:
            print("ERROR: pyuic5 not found. Please install PyQt5-tools:")
            print("  pip install PyQt5-tools")
            return False

def compile_ui_file(ui_file, py_file):
    """Compile a single .ui file to .py file"""
    ui_path = Path(ui_file)
    py_path = Path(py_file)
    
    if not ui_path.exists():
        print(f"WARNING: {ui_file} not found, skipping...")
        return False
    
    print(f"Compiling {ui_file} -> {py_file}")
    
    try:
        # Try pyuic5 command first
        result = subprocess.run(['pyuic5', str(ui_path), '-o', str(py_path)], 
                              capture_output=True, text=True, check=True)
        print(f"  ✓ Successfully compiled {ui_file}")
        return True
    except (FileNotFoundError, subprocess.CalledProcessError):
        try:
            # Fallback to python module method
            result = subprocess.run([sys.executable, '-m', 'PyQt5.uic.pyuic', 
                                   str(ui_path), '-o', str(py_path)], 
                                  capture_output=True, text=True, check=True)
            print(f"  ✓ Successfully compiled {ui_file}")
            return True
        except subprocess.CalledProcessError as e:
            print(f"  ✗ Failed to compile {ui_file}: {e.stderr}")
            return False

def main():
    """Main compilation function"""
    print("=== UI Compilation Script ===")
    print("Compiling .ui files to .py files for WPX-QT-GUI\n")
    
    # Check if pyuic5 is available
    if not check_pyuic5():
        sys.exit(1)
    
    # Get script directory
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    success_count = 0
    total_count = len(UI_FILES)
    
    # Compile each UI file
    for ui_file, py_file in UI_FILES.items():
        if compile_ui_file(ui_file, py_file):
            success_count += 1
    
    print(f"\n=== Compilation Summary ===")
    print(f"Successfully compiled: {success_count}/{total_count} files")
    
    if success_count == total_count:
        print("✓ All UI files compiled successfully!")
    else:
        print(f"✗ {total_count - success_count} files failed to compile")
        sys.exit(1)

if __name__ == "__main__":
    main()