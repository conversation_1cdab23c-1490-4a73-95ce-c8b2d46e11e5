import serial
import threading
import time
import os
import traceback
import sys
from typing import Optional, Callable
from PyQt5.QtCore import QObject, pyqtSignal
from ymodem_update.Ymodem import Ymodem
from firmware_downloader import FirmwareDownloader
from controller.wpx_protocol import WPXProtocol

# 动态添加路径以支持导入
current_dir = os.path.dirname(__file__)
parent_dir = os.path.dirname(current_dir)
sys.path.append(current_dir)
sys.path.append(parent_dir)

try:
    from loguru import logger
except ImportError:
    # 如果loguru不可用，使用标准logging
    import logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    # 确保logger具有loguru的方法签名
    if not hasattr(logger, 'success'):
        logger.success = logger.info

from firmware_downloader import FirmwareDownloader
from ymodem_update.Ymodem import YmodemFromMemory, YmodemFromMemoryStable

try:
    from controller.wpx_protocol import WPXProtocol
    from controller import wait_for_bootloader_device, wait_for_bootloader_device_with_callback, get_all_serial_devices_info
except ImportError:
    # 尝试不同的导入路径
    sys.path.append(os.path.join(parent_dir, 'controller'))
    from controller.wpx_protocol import WPXProtocol
    from controller import wait_for_bootloader_device, wait_for_bootloader_device_with_callback, get_all_serial_devices_info


class AutoFirmwareFlasher:
    """自动固件烧录器"""
    
    def __init__(self, serial_port, wpx_protocol: Optional[WPXProtocol]):
        import threading
        self.serial_port = serial_port
        self.wpx_protocol = wpx_protocol
        # 串口操作线程锁，保证写入和读取的原子性
        self.serial_lock = threading.Lock()
        # wpx_protocol可以为None（用于bootloader模式）
        if wpx_protocol is None:
            logger.info("初始化AutoFirmwareFlasher：Bootloader模式（无WPX协议）")
        else:
            logger.info("初始化AutoFirmwareFlasher：正常模式（使用WPX协议）")
        self.downloader = FirmwareDownloader(memory_only=True)  # 启用纯内存模式
        
        # UI状态更新队列 - 完全解耦UI和传输逻辑
        import queue
        self.ui_status_queue = queue.Queue()
        self.ui_progress_queue = queue.Queue()
        
        # 回调函数（仅用于注册）
        self.progress_callback: Optional[Callable[[int], None]] = None
        self.status_callback: Optional[Callable[[str], None]] = None
        self.complete_callback: Optional[Callable[[bool], None]] = None
        self.multi_device_callback: Optional[Callable[[list], str]] = None
        self.debug_callback: Optional[Callable[[], None]] = None
        
        # 烧录状态
        self.is_flashing = False
        self.current_step = ""
        
        # 启动UI更新守护线程
        self._start_ui_update_daemon()
        self.total_steps = 5
        self.current_step_num = 0
    
    def set_progress_callback(self, callback: Callable[[int], None]):
        """设置进度回调函数"""
        self.progress_callback = callback
        self.downloader.set_progress_callback(self._download_progress_callback)
    
    def set_status_callback(self, callback: Callable[[str], None]):
        """设置状态回调函数"""
        self.status_callback = callback
        self.downloader.set_status_callback(self._download_status_callback)
    
    def set_complete_callback(self, callback: Callable[[bool], None]):
        """设置完成回调函数"""
        self.complete_callback = callback
    
    def set_multi_device_callback(self, callback: Callable[[list], str]):
        """设置多设备选择回调函数"""
        self.multi_device_callback = callback
    
    def set_debug_callback(self, callback: Callable[[], None]):
        """设置调试信息回调函数"""
        self.debug_callback = callback
    
    def _ymodem_memory_with_timeout(self, firmware_data, file_name, serial_port, text_view, progress_callback, timeout=300):
        """带超时保护的内存Ymodem调用"""
        import threading
        import queue
        
        result_queue = queue.Queue()
        ymodem_thread = None
        
        def ymodem_worker():
            try:
                result = YmodemFromMemoryStable(firmware_data, file_name, serial_port, text_view, progress_callback)
                logger.info(f"Ymodem worker completed with result: {result}")
                result_queue.put(('success', result))
            except Exception as e:
                error_msg = str(e)
                logger.error(f"Ymodem worker exception: {error_msg}")
                
                # 特殊处理设备断开连接的情况
                if "PermissionError" in error_msg or "设备不识别此命令" in error_msg:
                    logger.info("Device disconnected during transfer - likely successful")
                    result_queue.put(('success', True))  # 认为是成功的
                else:
                    result_queue.put(('error', error_msg))
        
        try:
            # 启动Ymodem线程
            ymodem_thread = threading.Thread(target=ymodem_worker)
            ymodem_thread.daemon = True
            ymodem_thread.start()
            
            # 等待结果或超时
            try:
                result_type, result_value = result_queue.get(timeout=timeout)
                logger.info(f"Received result from worker: type={result_type}, value={result_value}")
                if result_type == 'success':
                    return result_value
                else:
                    logger.error(f"Ymodem worker error: {result_value}")
                    return False
            except queue.Empty:
                # 超时 - 检查是否可能是设备断开连接导致的
                logger.warning(f"Ymodem timeout after {timeout} seconds")
                
                # 检查串口状态
                try:
                    if serial_port and serial_port.is_open:
                        # 串口仍然打开，这是真正的超时
                        self._update_status(f"Ymodem传输超时 ({timeout}秒)")
                        serial_port.close()
                        logger.info("Serial port forcibly closed due to timeout")
                        return False
                    else:
                        # 串口已关闭，可能是设备断开连接，在高进度时认为成功
                        logger.info("Serial port already closed - device likely disconnected after successful transfer")
                        self._update_status("设备断开连接，传输可能已成功完成")
                        return True
                except Exception as e:
                    logger.error(f"Error checking serial port status: {e}")
                    # 如果无法检查串口状态，在高超时值的情况下可能是成功的
                    if timeout >= 600:  # 10分钟超时通常意味着传输时间足够
                        logger.info("Long timeout suggests transfer may have completed successfully")
                        self._update_status("长时间超时，传输可能已完成")
                        return True
                    return False
                
        except Exception as e:
            logger.error(f"Ymodem timeout wrapper error: {e}")
            return False
        finally:
            # 确保线程清理
            if ymodem_thread and ymodem_thread.is_alive():
                # 注意：这里不能强制杀死线程，只能等待它自然结束
                logger.warning("Ymodem thread still alive after timeout")
    
    def _ymodem_with_timeout(self, firmware_path, serial_port, text_view, progress_callback, timeout=300):
        """带超时保护的Ymodem调用"""
        import threading
        import queue
        
        result_queue = queue.Queue()
        ymodem_thread = None
        
        def ymodem_worker():
            try:
                # 强制使用YmodemFromMemoryStable而不是原始Ymodem
                # 先读取固件文件到内存
                with open(firmware_path, 'rb') as f:
                    firmware_data = f.read()
                file_name = os.path.basename(firmware_path)
                
                # 使用修复后的YmodemFromMemoryStable函数
                result = YmodemFromMemoryStable(firmware_data, file_name, serial_port, text_view, progress_callback)
                result_queue.put(('success', result))
            except Exception as e:
                result_queue.put(('error', str(e)))
        
        try:
            # 启动Ymodem线程
            ymodem_thread = threading.Thread(target=ymodem_worker)
            ymodem_thread.daemon = True
            ymodem_thread.start()
            
            # 等待结果或超时
            try:
                result_type, result_value = result_queue.get(timeout=timeout)
                if result_type == 'success':
                    return result_value
                else:
                    logger.error(f"Ymodem worker error: {result_value}")
                    return False
            except queue.Empty:
                # 超时
                self._update_status(f"Ymodem传输超时 ({timeout}秒)")
                logger.warning(f"Ymodem timeout after {timeout} seconds")
                
                # 强制关闭串口来中断可能的阻塞
                try:
                    if serial_port and serial_port.is_open:
                        serial_port.close()
                        logger.info("Serial port forcibly closed due to timeout")
                except Exception as e:
                    logger.error(f"Error closing serial port: {e}")
                
                return False
                
        except Exception as e:
            logger.error(f"Ymodem timeout wrapper error: {e}")
            return False
        finally:
            # 确保线程清理
            if ymodem_thread and ymodem_thread.is_alive():
                # 注意：这里不能强制杀死线程，只能等待它自然结束
                logger.warning("Ymodem thread still alive after timeout")
    
    def _start_ui_update_daemon(self):
        """启动UI更新守护线程 - 完全独立于数据传输"""
        import threading
        
        def ui_update_worker():
            """UI更新工作线程 - 独立处理所有UI更新"""
            while True:
                try:
                    # 处理状态更新
                    try:
                        message = self.ui_status_queue.get(timeout=0.1)
                        if message is None:  # 退出信号
                            break
                        if self.status_callback:
                            try:
                                self.status_callback(message)
                            except Exception as ui_error:
                                logger.error(f"UI status callback error (ignored): {ui_error}")
                        self.ui_status_queue.task_done()
                    except:
                        pass  # 超时正常，继续
                    
                    # 处理进度更新
                    try:
                        progress = self.ui_progress_queue.get(timeout=0.1)
                        if progress is None:  # 退出信号
                            break
                        if self.progress_callback:
                            try:
                                self.progress_callback(progress)
                            except Exception as ui_error:
                                logger.error(f"UI progress callback error (ignored): {ui_error}")
                        self.ui_progress_queue.task_done()
                    except:
                        pass  # 超时正常，继续
                        
                except Exception as e:
                    logger.error(f"UI update daemon error: {e}")
                    # 即使守护线程出错也不影响传输
        
        # 创建并启动守护线程
        self.ui_daemon = threading.Thread(target=ui_update_worker, daemon=True)
        self.ui_daemon.start()
        logger.info("UI update daemon started")
    
    def stop_ui_daemon(self):
        """停止UI更新守护线程"""
        try:
            # 发送退出信号
            self.ui_status_queue.put_nowait(None)
            self.ui_progress_queue.put_nowait(None)
        except:
            pass
    
    def _download_progress_callback(self, progress: int):
        """下载进度回调"""
        # 下载是第1步，占总进度的20%
        overall_progress = int(progress * 0.2)
        logger.debug(f"Download progress: {progress}% -> Overall: {overall_progress}%")
        self._update_progress(overall_progress)
    
    def _download_status_callback(self, status: str):
        """下载状态回调"""
        self._update_status(f"[1/5] {status}")
    
    def _update_status(self, message: str):
        """更新状态信息 - 非阻塞投递到UI队列，增强可靠性"""
        logger.info(f"Status update: {message}")
        
        # 增强的UI更新机制 - 多重保障
        ui_updated = False
        
        # 尝试1: 非阻塞队列投递
        try:
            self.ui_status_queue.put_nowait(message)
            ui_updated = True
        except:
            logger.debug("UI status queue full, trying direct callback")
        
        # 尝试2: 直接回调（bootloader模式的关键修复）
        if not ui_updated and self.status_callback:
            try:
                self.status_callback(message)
                ui_updated = True
                logger.debug(f"Direct status callback succeeded: {message}")
            except Exception as e:
                logger.warning(f"Direct status callback failed: {e}")
        
        # 记录UI更新状态
        if not ui_updated:
            logger.warning(f"Both queue and direct status update failed for: {message}")
    
    # 删除了老的阻塞式方法，现在使用队列机制
    
    def _safe_status_update(self, message: str):
        """安全的状态更新，使用非阻塞方式"""
        logger.info(f"_safe_status_update called with: {message}")
        try:
            # 直接使用新的非阻塞更新机制
            self._update_status(message)
        except Exception as e:
            logger.error(f"Safe status update error: {e}")
    
    def _safe_serial_connect(self, port: str, baudrate: int, device_type: str, max_retries: int = 3) -> bool:
        """安全的串口连接方法，包含重试和错误处理
        
        Args:
            port: 串口名称
            baudrate: 波特率
            device_type: 设备类型描述
            max_retries: 最大重试次数
            
        Returns:
            bool: 连接成功返回True，失败返回False
        """
        import time
        
        # 关键修复：强制bootloader设备使用115200波特率
        if "bootloader" in device_type.lower() or "Bootloader" in device_type:
            original_baudrate = baudrate
            baudrate = 115200
            logger.info(f"检测到bootloader设备，强制使用115200波特率 (原波特率: {original_baudrate})")
            self._update_status(f"设置bootloader波特率: {baudrate}")
        
        for attempt in range(max_retries):
            try:
                # 强制关闭现有连接
                if self.serial_port.is_open:
                    self.serial_port.close()
                    time.sleep(0.2)  # 增加等待时间确保端口完全关闭
                
                # 重新创建串口对象以避免状态污染
                import serial
                self.serial_port.port = port
                self.serial_port.baudrate = baudrate  # 使用修复后的波特率
                self.serial_port.timeout = 5.0
                self.serial_port.write_timeout = 5.0  # 设置写超时
                
                # 尝试打开串口
                self.serial_port.open()
                time.sleep(0.3)
                
                # 验证连接状态
                if not self.serial_port.is_open:
                    raise Exception("串口未能正确打开")
                
                # 清空串口缓冲区
                if self.serial_port.in_waiting > 0:
                    self.serial_port.read_all()
                
                self._update_status(f"成功连接到{device_type}: {port}")
                logger.info(f"Successfully connected to {device_type}: {port}")
                return True
                
            except Exception as e:
                error_msg = str(e)
                logger.error(f"Serial connection attempt {attempt + 1}/{max_retries} failed: {error_msg}")
                
                # 特殊处理Windows信号灯超时错误
                if "SError(22" in error_msg or "信号灯超时" in error_msg:
                    self._update_status(f"串口超时错误，尝试重置串口... ({attempt + 1}/{max_retries})")
                    # 强制关闭串口并等待更长时间
                    try:
                        if self.serial_port.is_open:
                            self.serial_port.close()
                    except:
                        pass
                    time.sleep(1.0)  # 等待更长时间让系统释放资源
                elif "Access is denied" in error_msg or "拒绝访问" in error_msg:
                    self._update_status(f"串口被占用，等待释放... ({attempt + 1}/{max_retries})")
                    time.sleep(2.0)  # 等待其他程序释放串口
                else:
                    self._update_status(f"串口连接失败: {error_msg} ({attempt + 1}/{max_retries})")
                    time.sleep(0.5)
                
                if attempt < max_retries - 1:
                    self._update_status(f"1秒后重试连接{device_type}...")
                    time.sleep(1.0)
        
        self._update_status(f"连接{device_type}失败，已尝试{max_retries}次")
        return False
    
    def _update_progress(self, progress: int):
        """更新进度 - 防止进度回滚和重复更新，增强可靠性"""
        # 防止进度回滚保护
        if not hasattr(self, '_last_overall_progress'):
            self._last_overall_progress = -1
        
        # 只允许进度向前或相等，防止回滚和减少重复更新
        if progress >= self._last_overall_progress:
            # 减少重复更新：进度变化<1%时跳过（但重要进度点强制更新）
            important_progress = progress in [0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 95, 100]
            if progress - self._last_overall_progress >= 1 or progress == 100 or important_progress:
                self._last_overall_progress = progress
                
                # 减少日志输出频率但确保重要进度记录
                if progress % 5 == 0 or progress == 100 or important_progress:
                    logger.info(f"Updating progress: {progress}%")
                
                # 增强的UI更新机制 - 多重保障
                ui_updated = False
                
                # 尝试1: 非阻塞队列投递
                try:
                    self.ui_progress_queue.put_nowait(progress)
                    ui_updated = True
                except:
                    logger.debug("UI progress queue full, trying direct callback")
                
                # 尝试2: 直接回调（bootloader模式的关键修复）
                if not ui_updated and self.progress_callback:
                    try:
                        self.progress_callback(progress)
                        ui_updated = True
                        logger.debug(f"Direct progress callback succeeded: {progress}%")
                    except Exception as e:
                        logger.warning(f"Direct progress callback failed: {e}")
                
                # 记录UI更新状态
                if not ui_updated:
                    logger.warning(f"Both queue and direct progress update failed for {progress}%")
                    
            else:
                logger.debug(f"跳过微小进度变化: {progress}% (上次: {self._last_overall_progress}%)")
        else:
            logger.warning(f"阻止进度回滚: {progress}% -> {self._last_overall_progress}%")
    
    # 删除了老的阻塞式方法，现在使用队列机制
    
    def _safe_progress_update(self, progress: int):
        """安全的进度更新，使用非阻塞方式"""
        if progress % 5 == 0:  # 调整日志频率但不过度严格
            logger.info(f"_safe_progress_update called with: {progress}%")
        try:
            # 直接使用新的非阻塞更新机制
            self._update_progress(progress)
        except Exception as e:
            logger.error(f"Safe progress update error: {e}")
    
    def _update_step(self, step_num: int, step_name: str):
        """更新当前步骤"""
        self.current_step_num = step_num
        self.current_step = step_name
        
        # 优化进度分配：减少固件下载，增加烧录步骤进度空间
        if self.total_steps == 5:  # 5步模式（单独系统固件更新）
            # 自定义进度分配：[5%, 10%, 15%, 25-45%, 100%]
            step_progress_map = {1: 5, 2: 10, 3: 15, 4: 25, 5: 100}
            base_progress = step_progress_map.get(step_num, int((step_num - 1) / self.total_steps * 100))
        elif self.total_steps == 4:  # 4步模式（全更新中的系统固件部分）
            # 全更新模式自定义进度分配：[5%, 10%, 25-45%, 50%]
            step_progress_map = {1: 5, 2: 10, 3: 25, 4: 50}
            base_progress = step_progress_map.get(step_num, int((step_num - 1) / self.total_steps * 100))
        else:
            # 默认均匀分配
            base_progress = int((step_num - 1) / self.total_steps * 100)
            
        logger.info(f"Step {step_num}/{self.total_steps}: {step_name} (Base progress: {base_progress}%)")
        
        # 关键修复：只有在没有子进度回调活跃时才设置步骤进度
        # 自动更新：步骤1(下载)和步骤4(烧录)有自己的进度回调
        # 全更新：步骤3(烧录)有自己的进度回调
        skip_steps = [1, 4]  # 自动更新跳过的步骤
        if self.total_steps == 4:  # 全更新模式
            skip_steps = [3, 4]  # 全更新跳过的步骤
        
        if step_num not in skip_steps:
            self._update_progress(base_progress)
            logger.info(f"设置步骤{step_num}基础进度: {base_progress}%")
        else:
            logger.info(f"跳过步骤{step_num}的基础进度设置，等待子进度回调")
        
        self._update_status(f"[{step_num}/{self.total_steps}] {step_name}")
    
    def start_auto_flash(self, force_download: bool = False) -> bool:
        """
        开始自动固件烧录流程 - 在独立线程中运行避免UI卡死
        
        Args:
            force_download: 保留参数以兼容调用方（固件始终从服务器下载）
            
        Returns:
            bool: 操作是否成功启动
        """
        if self.is_flashing:
            self._update_status("烧录正在进行中，请勿重复操作")
            return False
        
        logger.info(f"Serial port check: port={self.serial_port}, is_open={self.serial_port.is_open if self.serial_port else 'N/A'}")
        
        # 固件更新器现在支持自动检测设备，不再要求预先连接
        # 设备检测和连接将在烧录过程中自动处理
        if not self.serial_port or not self.serial_port.is_open:
            self._update_status("无预连接设备，将在烧录过程中自动检测...")
            logger.info("No pre-connected device, auto-detection will be handled in flash process")
        else:
            self._update_status("使用已连接设备开始烧录...")
            logger.info("Using pre-connected device for flashing")
        
        # 立即返回，在独立线程中执行实际烧录
        import threading
        flash_thread = threading.Thread(target=self._run_flash_process, args=(force_download,))
        flash_thread.daemon = True
        flash_thread.start()
        
        logger.info("Auto firmware flash thread started")
        return True
    
    def _check_firmware_availability(self, force_download: bool) -> bool:
        """
        检查固件可用性 - 始终从服务器下载到内存中
        
        Args:
            force_download: 是否强制重新下载（保留参数以兼容调用方）
            
        Returns:
            bool: 固件可用返回True，否则返回False
        """
        try:
            # 始终从服务器下载固件到内存中
            self._update_status("开始从服务器下载固件...")
            logger.info("Starting firmware download from server...")
            
            download_success = self.downloader.download_firmware(True)  # 总是强制下载
            if download_success:
                self._update_status("固件下载成功")
                logger.info("Firmware download successful")
                return True
            else:
                self._update_status("固件下载失败")
                logger.error("Firmware download failed")
                return False
                
        except Exception as e:
            logger.error(f"Error downloading firmware: {e}")
            self._update_status(f"固件下载出错: {str(e)}")
            return False
    
    def _run_flash_process(self, force_download: bool):
        """在独立线程中运行的烧录流程"""
        self.is_flashing = True
        logger.info("Starting auto firmware flash process in background thread")
        
        try:
            # 步骤1: 下载固件到内存
            self._update_step(1, "下载固件到内存")
            firmware_available = self._check_firmware_availability(force_download)
            if not firmware_available:
                self._update_status("固件下载失败")
                self._complete_flash(False)
                return
            
            # 步骤2: 检查固件文件
            self._update_step(2, "检查固件文件")
            try:
                firmware_info = self.downloader.get_firmware_info()
                if not firmware_info.get("exists", False):
                    self._update_status("固件文件不存在")
                    self._complete_flash(False)
                    return
                
                size_mb = firmware_info.get('size_mb', 0)
                memory_mode = firmware_info.get('memory_mode', False)
                location = "内存中" if memory_mode else "本地文件"
                status_message = f"固件文件大小: {size_mb:.2f} MB ({location})"
                self._update_status(status_message)
                
            except Exception as info_error:
                logger.error(f"Error checking firmware info: {info_error}")
                import traceback
                traceback.print_exc()
                self._update_status("检查固件文件出错")
                self._complete_flash(False)
                return
            
            # 步骤3: 进入Bootloader模式
            self._update_step(3, "进入Bootloader模式")
            logger.info("Starting bootloader entry process...")
            bootloader_result = self._enter_bootloader()
            logger.info(f"Bootloader entry result: {bootloader_result}")
            
            if not bootloader_result:
                logger.error("Bootloader entry failed - aborting flash process")
                self._update_status("进入Bootloader模式失败")
                self._complete_flash(False)
                return
            
            logger.info("Bootloader entry successful, proceeding to firmware flash...")
            
            # 步骤4: 烧录固件
            self._update_step(4, "烧录固件")
            # 修复进度竞争：手动设置烧录起始进度
            self._update_progress(25)
            logger.info("Starting firmware flash process...")
            flash_result = self._flash_firmware()
            logger.info(f"Firmware flash result: {flash_result}")
            
            if not flash_result:
                logger.error("Firmware flash failed")
                self._update_status("固件烧录失败")
                self._complete_flash(False)
                return
            
            # 步骤5: 完成烧录
            self._update_step(5, "烧录完成")
            # 修复全更新进度回退：检查是否是全更新模式来决定是否设置100%
            # 在全更新模式下，系统固件更新完成时保持在94%左右，让内核更新完成最后的进度
            # 在单独系统更新模式下，正常设置为100%
            if not (hasattr(self, 'total_steps') and self.total_steps == 4):
                # 非全更新模式（total_steps != 4），正常设置为100%
                self._update_progress(100)
                logger.info("单独系统固件更新完成，设置进度为100%")
            else:
                # 全更新模式，不设置100%，让内核更新来完成
                logger.info("全更新模式系统固件完成，保持当前进度不变")
            self._update_status("固件烧录成功完成")
            self._complete_flash(True)
            
        except Exception as e:
            logger.error(f"Auto flash error: {e}")
            import traceback
            traceback.print_exc()
            self._update_status(f"烧录过程出错: {str(e)}")
            self._complete_flash(False)
    
    def _enter_bootloader(self, max_retries: int = 3) -> bool:
        """进入Bootloader模式 - 支持重试和重连机制"""
        for attempt in range(max_retries):
            try:
                if attempt > 0:
                    self._update_status(f"[第{attempt + 1}次尝试] 进入Bootloader模式")
                    logger.info(f"Bootloader entry retry attempt {attempt + 1}/{max_retries}")
                    
                    # 重连机制：在重试前尝试重新建立连接
                    if self._reconnect_device_before_retry():
                        logger.info("Device reconnected successfully before retry")
                    else:
                        logger.warning("Device reconnection failed, continuing with retry anyway")
                
                result = self._enter_bootloader_attempt()
                if result:
                    return True
                    
            except Exception as e:
                logger.error(f"Bootloader entry attempt {attempt + 1} failed: {e}")
                if attempt < max_retries - 1:
                    self._update_status(f"进入Bootloader失败，3秒后重试...")
                    time.sleep(3)  # 等待3秒后重试
                else:
                    self._update_status(f"进入Bootloader模式失败：尝试{max_retries}次后仍无法进入")
                    logger.error(f"All {max_retries} bootloader entry attempts failed")
                    return False
        
        return False
    
    def _reconnect_device_before_retry(self) -> bool:
        """在重试前尝试重新连接设备"""
        try:
            self._update_status("尝试重新连接设备...")
            
            # 1. 关闭当前连接
            if self.serial_port and self.serial_port.is_open:
                self.serial_port.close()
                time.sleep(1)  # 等待端口释放
            
            # 2. 清理WPX协议状态
            if self.wpx_protocol and hasattr(self.wpx_protocol, '_ser'):
                if self.wpx_protocol._ser and self.wpx_protocol._ser.is_open:
                    self.wpx_protocol._ser.close()
                    time.sleep(0.5)
                # 清除引用
                self.wpx_protocol._ser = None
            
            # 3. 重新扫描和连接设备
            from controller import find_all_devices
            devices = find_all_devices()
            
            if devices:
                # 使用第一个可用设备
                port, description = devices[0]
                logger.info(f"Attempting to reconnect to device: {port}")
                
                # 重新初始化串口连接  
                success = self._safe_serial_connect(port, 921600, "正常模式设备")
                if not success:
                    self._update_status("重连设备失败")
                    self._complete_flash(False)
                    return
                
                # 重新初始化WPX协议
                if self.wpx_protocol:
                    self.wpx_protocol._ser = self.serial_port
                    # 清空缓冲区
                    if self.serial_port.in_waiting > 0:
                        self.serial_port.read_all()
                
                self._update_status(f"设备重连成功: {port}")
                return True
            else:
                logger.warning("No normal mode devices found for reconnection")
                return False
                
        except Exception as e:
            logger.error(f"Device reconnection failed: {e}")
            return False
    
    def _enter_bootloader_attempt(self) -> bool:
        """单次Bootloader进入尝试"""
        
        # 预清理：彻底清理WPX协议状态，避免影响bootloader通信
        # 这是解决正常模式高失败率的关键修复
        if self.wpx_protocol is not None:
            try:
                logger.info("开始清理WPX协议状态，防止状态污染...")
                
                # 1. 停止所有活跃的协议操作
                if hasattr(self.wpx_protocol, 'stop_all_operations'):
                    self.wpx_protocol.stop_all_operations()
                    logger.debug("已停止WPX协议操作")
                
                # 2. 彻底清空串口缓冲区
                if hasattr(self.wpx_protocol, '_ser') and self.wpx_protocol._ser:
                    if self.wpx_protocol._ser.is_open:
                        # 多次清空确保彻底
                        for _ in range(3):
                            self.wpx_protocol._ser.reset_input_buffer()
                            self.wpx_protocol._ser.reset_output_buffer()
                            import time
                            time.sleep(0.1)
                        logger.debug("已清空WPX串口缓冲区")
                
                # 3. 重置协议内部状态
                if hasattr(self.wpx_protocol, 'reset_state'):
                    self.wpx_protocol.reset_state()
                    logger.debug("已重置WPX协议内部状态")
                
                # 4. 清理可能的超时定时器和线程
                if hasattr(self.wpx_protocol, '_cleanup_timers'):
                    self.wpx_protocol._cleanup_timers()
                
                # 5. 额外延时确保状态完全清理
                import time
                time.sleep(0.5)
                
                logger.info("WPX协议状态清理完成，环境已接近bootloader模式")
                
            except Exception as cleanup_error:
                logger.warning(f"WPX协议状态清理警告: {cleanup_error}")
                # 不阻断流程，但记录问题以便调试
                if hasattr(self, 'debug_callback') and self.debug_callback:
                    self.debug_callback()
        else:
            logger.info("设备已在bootloader模式，无需清理WPX协议状态")
        
        # 第一步：检查是否已经存在bootloader设备
        self._update_status("检查是否已存在Bootloader设备...")
        logger.info("Checking for existing bootloader devices")
        
        try:
            from controller import find_bootloader_devices
            existing_bootloader_devices = find_bootloader_devices()
            
            if existing_bootloader_devices:
                # 发现已存在的bootloader设备，直接使用
                if len(existing_bootloader_devices) == 1:
                    bootloader_port = existing_bootloader_devices[0][0]
                    self._update_status(f"发现已存在的Bootloader设备: {bootloader_port}")
                    logger.info(f"Found existing bootloader device: {bootloader_port}")
                else:
                    # 多个bootloader设备，让用户选择
                    if self.multi_device_callback:
                        bootloader_port = self.multi_device_callback(existing_bootloader_devices)
                        if not bootloader_port:
                            self._update_status("用户取消了设备选择")
                            return False
                        self._update_status(f"用户选择了Bootloader设备: {bootloader_port}")
                    else:
                        # 没有回调，使用第一个
                        bootloader_port = existing_bootloader_devices[0][0]
                        self._update_status(f"使用第一个Bootloader设备: {bootloader_port}")
                # 延时一下下
                time.sleep(0.5)  # 延时0.5秒
                
                # 连接前的最终资源清理
                import time
                time.sleep(0.2)  # 确保资源释放完成
                
                # 直接连接到现有的bootloader设备
                success = self._safe_serial_connect(bootloader_port, 115200, "现有Bootloader设备")
                if success:
                    # 关键修复：专注于WPX残留数据清理，避免任何可能产生数据的测试
                    logger.info("设备连接成功，开始WPX残留数据清理...")
                    self._update_status("设备已连接，正在清理WPX残留数据...")
                    
                    # 只进行稳定性检查和数据清理，不进行任何会产生数据的测试
                    stability_ok = self._verify_bootloader_stability()
                    
                    logger.info("WPX残留数据清理完成，设备准备进行Ymodem通信")
                    
                    return True
                
        except Exception as check_error:
            logger.warning(f"Error checking for existing bootloader devices: {check_error}")
            # 检查失败，继续尝试发送命令
        
        # 第二步：没有发现现有bootloader设备，尝试发送进入Bootloader命令
        command_sent = False
        if self.wpx_protocol is not None:
            try:
                self._update_status("发送进入Bootloader命令...")
                logger.info("Sending bootloader command via WPX protocol")
                
                # 增强的命令发送重试机制
                for cmd_attempt in range(2):  # 尝试2次
                    try:
                        if cmd_attempt > 0:
                            logger.info(f"Retrying bootloader command send (attempt {cmd_attempt + 1})")
                            # 清空缓冲区后重试
                            if hasattr(self.wpx_protocol, '_ser') and self.wpx_protocol._ser:
                                if self.wpx_protocol._ser.is_open:
                                    self.wpx_protocol._ser.reset_input_buffer()
                                    self.wpx_protocol._ser.reset_output_buffer()
                                    time.sleep(0.2)
                        
                        self.wpx_protocol.enter_bootloader_cmd()
                        time.sleep(1.5)  # 增加等待时间，给设备充分时间切换到bootloader模式
                        command_sent = True
                        logger.info("Bootloader command sent successfully")
                        break  # 成功发送，退出重试循环
                        
                    except Exception as retry_error:
                        logger.warning(f"Bootloader command send attempt {cmd_attempt + 1} failed: {retry_error}")
                        if cmd_attempt == 1:  # 最后一次尝试
                            raise retry_error
                
            except Exception as cmd_error:
                logger.warning(f"All bootloader command send attempts failed: {cmd_error}")
                self._update_status("命令发送失败，但继续检测Bootloader设备...")
                # 不返回False，继续执行检测逻辑
        else:
            logger.info("WPX协议不可用（Bootloader模式），跳过命令发送")
            self._update_status("设备已在Bootloader模式，跳过命令发送")
        
        # 第三步：等待新的bootloader设备出现
        self._update_status("等待设备进入Bootloader模式...")
        logger.info(f"Waiting for bootloader device to appear (command_sent: {command_sent})")
        
        # 关键调试：显示当前设备状态
        try:
            from controller import get_all_serial_devices_info
            current_devices = get_all_serial_devices_info()
            logger.info(f"当前可见设备: {current_devices}")
        except Exception as e:
            logger.warning(f"无法获取设备列表: {e}")
        
        try:
            import signal
            import threading
            
            bootloader_port = None
            detection_complete = threading.Event()
            
            def detect_bootloader():
                nonlocal bootloader_port
                try:
                    # 使用更快的检测参数
                    from controller import wait_for_bootloader_device_with_callback
                    bootloader_port = wait_for_bootloader_device_with_callback(
                        timeout=8.0,  # 减少到8秒
                        check_interval=0.3,  # 更频繁的检查
                        multi_device_callback=self.multi_device_callback
                    )
                    logger.info(f"Bootloader detection completed: {bootloader_port}")
                except Exception as e:
                    logger.error(f"Bootloader detection error: {e}")
                    bootloader_port = None
                finally:
                    detection_complete.set()
            
            # 在独立线程中运行检测，避免阻塞
            detection_thread = threading.Thread(target=detect_bootloader)
            detection_thread.daemon = True
            detection_thread.start()
            
            # 等待检测完成，最长10秒
            if detection_complete.wait(timeout=10.0):
                logger.info(f"Bootloader detection finished: {bootloader_port}")
            else:
                logger.warning("Bootloader detection timeout after 10 seconds")
                bootloader_port = None
                
        except Exception as detection_error:
            logger.error(f"Bootloader detection setup error: {detection_error}")
            bootloader_port = None
        
        # 第四步：检查检测结果并建立连接
        if bootloader_port:
            if command_sent:
                self._update_status(f"检测到新的Bootloader设备: {bootloader_port}")
            else:
                self._update_status(f"检测到Bootloader设备: {bootloader_port} (命令发送失败但设备已进入)")
                
            # 更新串口为bootloader端口
            success = self._safe_serial_connect(bootloader_port, 115200, "Bootloader设备")
            if success:
                logger.info(f"Successfully connected to bootloader (command_sent: {command_sent})")
                
                # 关键修复：新检测到的bootloader设备也需要稳定等待
                logger.info("新bootloader设备连接成功，开始稳定性检测...")
                self._update_status("新设备已连接，正在验证bootloader稳定性...")
                
                if self._verify_bootloader_stability():
                    logger.info("新bootloader设备稳定性验证通过")
                else:
                    logger.warning("新bootloader设备稳定性验证失败，但继续尝试")
                
                return True
            else:
                self._update_status(f"连接Bootloader设备失败")
                return False
        else:
            if command_sent:
                self._update_status("超时: 未检测到Bootloader设备")
            else:
                self._update_status("命令发送失败且未检测到Bootloader设备")
            logger.warning(f"No bootloader device detected (command_sent: {command_sent})")
            return False
    
    
    
    def _flash_firmware(self) -> bool:
        """烧录固件（纯内存模式，无文件IO）"""
        try:
            # 检查是否有可用的固件数据
            if not self.downloader.is_firmware_available():
                self._update_status("固件不可用")
                return False
            
            firmware_data = self.downloader.get_firmware_data()
            if not firmware_data:
                self._update_status("固件数据不存在")
                return False
            
            # 检查串口状态
            if not self.serial_port or not self.serial_port.is_open:
                self._update_status("串口未打开，无法烧录固件")
                return False
            
            self._update_status(f"开始烧录固件（大小: {len(firmware_data) / 1024:.1f} KB）")
            
            # 创建一个简单的文本视图类来处理Ymodem的输出
            class FlashTextView:
                def __init__(self, flasher):
                    self.flasher = flasher
                
                def append(self, text):
                    try:
                        if text and isinstance(text, str):
                            self.flasher._update_status(f"烧录: {text.strip()}")
                    except Exception as e:
                        logger.error(f"FlashTextView append error: {e}")
                
                def set_status(self, text):
                    try:
                        if text and isinstance(text, str):
                            self.flasher._update_status(f"状态: {text.strip()}")
                    except Exception as e:
                        logger.error(f"FlashTextView set_status error: {e}")
            
            text_view = FlashTextView(self)
            
            # 使用Ymodem协议烧录固件
            # 添加进度缓存，避免过度频繁的UI更新
            self._last_flash_progress = -1
            
            def flash_progress_callback(progress):
                try:
                    # 修复进度竞争：烧录进度完全控制25%-45%区间
                    if isinstance(progress, (int, float)) and 0 <= progress <= 100:
                        # 扩大烧录进度空间：系统固件烧录进度从25%开始，到45%结束（20%的空间）
                        overall_progress = 25 + int(progress * 0.20)  # 0-100% 映射到 25-45%
                        
                        # 防止进度回滚的保护机制
                        if not hasattr(self, '_last_flash_progress'):
                            self._last_flash_progress = 25
                        
                        # 增加更新频率：每个数据包都更新进度，避免跳跃感
                        if overall_progress >= self._last_flash_progress:
                            self._last_flash_progress = overall_progress
                            # 减少日志频率但保持进度更新的平滑性
                            if overall_progress % 3 == 0:  # 每3%记录一次，减少日志量
                                logger.debug(f"Flash progress: {progress}% -> Overall: {overall_progress}%")
                            # 更新进度（每次都更新，确保平滑）
                            self._update_progress(overall_progress)
                        # 不记录跳过的微小变化，避免日志污染
                    else:
                        logger.warning(f"Invalid flash progress value: {progress}")
                except Exception as e:
                    logger.error(f"Flash progress callback error: {e}")
            
            # 设置超时保护
            # self._update_status("正在初始化Ymodem传输...")
            
            try:
                # 关键优化：在开始Ymodem传输前，彻底清空串口缓冲区
                # 这可以防止任何先前操作的残留数据干扰Ymodem协议的起始握手
                self._update_status("清空串口缓冲区...")
                logger.info("Clearing serial buffers before Ymodem transfer.")
                self.serial_port.reset_input_buffer()
                self.serial_port.reset_output_buffer()
                time.sleep(0.1) # 短暂延时确保缓冲区完成清空

                # 开始Ymodem传输（使用纯内存版本）
                self._update_status("开始固件传输...")
                
                # 使用超时保护的内存Ymodem调用
                success = self._ymodem_memory_with_timeout(
                    firmware_data,
                    "WPX_MPP_V2.bin",  # 固件文件名
                    self.serial_port, 
                    text_view, 
                    flash_progress_callback,
                    timeout=600  # 10分钟超时，为重试机制预留时间
                )
                
                if success:
                    self._update_status("固件烧录成功")
                    return True
                else:
                    self._update_status("固件烧录失败或超时")
                    return False
                    
            except Exception as ymodem_error:
                logger.error(f"Ymodem error: {ymodem_error}")
                import traceback
                traceback.print_exc()
                self._update_status(f"Ymodem传输错误: {str(ymodem_error)}")
                return False
                
        except Exception as e:
            logger.error(f"Flash firmware error: {e}")
            self._update_status(f"烧录过程出错: {str(e)}")
            return False
    
    def _complete_flash(self, success: bool):
        """完成烧录 - 使用Qt信号确保在主线程中执行"""
        self.is_flashing = False
        logger.info(f"Firmware flash completed: {success}")
        
        # 直接调用回调（让GUI层面处理线程安全）
        self._complete_flash_main_thread(success)
    
    def _complete_flash_main_thread(self, success: bool):
        """在主线程中执行的完成回调"""
        if self.complete_callback:
            try:
                logger.info(f"Calling complete_callback with success={success}")
                self.complete_callback(success)
                logger.info("Complete_callback finished successfully")
            except Exception as e:
                logger.error(f"Error in complete callback: {e}")
                import traceback
                traceback.print_exc()
        else:
            logger.warning("No complete callback set")
        
        # 最后更新状态信息（这会在主线程中执行，所以安全）
        final_message = "固件烧录成功完成" if success else "固件烧录失败"
        try:
            if self.status_callback:
                self.status_callback(final_message)
        except Exception as e:
            logger.debug(f"Final status update error (ignored): {e}")
    
    
    def stop_flash(self):
        """停止烧录（如果正在进行）"""
        if self.is_flashing:
            self.is_flashing = False
            self._update_status("正在停止烧录操作...")
            
            # 强制关闭串口来中断可能的阻塞
            try:
                if self.serial_port and self.serial_port.is_open:
                    self.serial_port.close()
                    logger.info("Serial port forcibly closed during stop")
            except Exception as e:
                logger.error(f"Error force closing serial port: {e}")
            
            self._update_status("烧录操作已停止")
    
    def _flash_firmware_direct(self, firmware_path: str = None) -> bool:
        """
        直接烧录固件（先从服务器下载到内存，然后烧录）
        
        Args:
            firmware_path: 保留参数以兼容调用方（实际会忽略，始终从服务器下载）
            
        Returns:
            bool: 烧录是否成功
        """
        if self.is_flashing:
            self._update_status("烧录正在进行中，请勿重复操作")
            return False
        
        if not self.serial_port or not self.serial_port.is_open:
            self._update_status("串口未连接，无法执行烧录")
            return False
        
        self.is_flashing = True
        logger.info("Starting direct firmware flash with server download")
        
        try:
            # 重置进度状态，避免与自动更新的进度冲突
            if hasattr(self, '_last_overall_progress'):
                self._last_overall_progress = -1
            
            # 调整全更新的total_steps为4（包含下载）
            self.total_steps = 4
            
            # 步骤1: 下载固件到内存
            self._update_step(1, "下载固件到内存")
            firmware_available = self._check_firmware_availability(True)  # 强制下载
            if not firmware_available:
                self._update_status("固件下载失败")
                self._complete_flash(False)
            
            # 步骤2: 检查固件文件
            self._update_step(2, "检查固件文件")
            try:
                firmware_info = self.downloader.get_firmware_info()
                if not firmware_info.get("exists", False):
                    self._update_status("固件文件不存在")
                    self._complete_flash(False)
                    return False
                
                size_mb = firmware_info.get('size_mb', 0)
                self._update_status(f"固件文件大小: {size_mb:.2f} MB (内存中)")
                
            except Exception as info_error:
                logger.error(f"Error checking firmware info: {info_error}")
                self._update_status("检查固件文件出错")
                self._complete_flash(False)
                return False
            
            # 步骤3: 进入Bootloader模式
            self._update_step(3, "进入Bootloader模式")
            if not self._enter_bootloader():
                self._update_status("进入Bootloader模式失败")
                self._complete_flash(False)
                return False
            
            # 步骤4: 烧录固件 - 不设置基础进度，让子回调控制
            self._update_step(4, "烧录固件")
            if not self._flash_firmware_with_path(None):  # 传入None，方法会使用内存数据
                self._update_status("固件烧录失败")
                self._complete_flash(False)
                return False
            
            # 烧录完成
            self._update_status("固件烧录成功完成")
            self._complete_flash(True)
            return True
            
        except Exception as e:
            logger.error(f"Direct flash error: {e}")
            self._update_status(f"烧录过程出错: {str(e)}")
            self._complete_flash(False)
            return False
    
    def _flash_firmware_with_path(self, firmware_path: str) -> bool:
        """使用内存中的固件数据进行烧录，线程锁保护"""
        try:
            with self.serial_lock:
                # 始终使用内存中的固件数据
                firmware_data = self.downloader.get_firmware_data()
                if not firmware_data:
                    self._update_status("内存中无固件数据，请先下载固件")
                    return False
                
                file_name = "WPX_MPP_V2.bin"
                self._update_status("使用内存中的固件数据")
                
                # 检查串口状态
                if not self.serial_port or not self.serial_port.is_open:
                    self._update_status("串口未打开，无法烧录固件")
                    return False
                
                self._update_status(f"开始烧录固件: {file_name} (大小: {len(firmware_data) / 1024:.1f} KB)")
                
                # 传输前Ymodem就绪性测试
                if not self._test_ymodem_readiness():
                    self._update_status("Ymodem就绪性测试未通过，终止烧录")
                    logger.error("Ymodem readiness test failed before flashing.")
                    return False
                # 创建一个简单的文本视图类来处理Ymodem的输出
                class FlashTextView:
                    def __init__(self, flasher):
                        self.flasher = flasher
                    def append(self, text):
                        try:
                            if text and isinstance(text, str):
                                self.flasher._update_status(f"烧录: {text.strip()}")
                        except Exception as e:
                            logger.error(f"FlashTextView append error: {e}")
                    def set_status(self, text):
                        try:
                            if text and isinstance(text, str):
                                self.flasher._update_status(f"状态: {text.strip()}")
                        except Exception as e:
                            logger.error(f"FlashTextView set_status error: {e}")
                text_view = FlashTextView(self)
                # 使用Ymodem协议烧录固件
                def flash_progress_callback(progress):
                    try:
                        # 直接固件更新模式：烧录进度占总进度的65%，从35%开始到100%
                        if isinstance(progress, (int, float)) and 0 <= progress <= 100:
                            overall_progress = 35 + int(progress * 0.65)  # 35%-100%
                            # 防止进度回滚和减少重复更新
                            if not hasattr(self, '_last_direct_flash_progress'):
                                self._last_direct_flash_progress = 35
                            if overall_progress >= self._last_direct_flash_progress:
                                self._last_direct_flash_progress = overall_progress
                                self._update_progress(overall_progress)
                    except Exception as e:
                        logger.error(f"Progress callback error: {e}")
                # 设置超时保护
                # self._update_status("正在初始化Ymodem传输...")
                
                try:
                    # 关键优化：在开始Ymodem传输前，彻底清空串口缓冲区
                    self._update_status("清空串口缓冲区...")
                    logger.info("Clearing serial buffers before Ymodem transfer.")
                    self.serial_port.reset_input_buffer()
                    self.serial_port.reset_output_buffer()
                    time.sleep(0.01) # 短暂延时确保缓冲区完成清空

                    # 开始Ymodem传输（使用纯内存版本）
                    self._update_status("开始固件传输...")
                    
                    # 使用超时保护的内存Ymodem调用
                    success = self._ymodem_memory_with_timeout(
                        firmware_data,
                        file_name,
                        self.serial_port, 
                        text_view, 
                        flash_progress_callback,
                        timeout=300  # 5分钟超时
                    )
                    
                    if success:
                        self._update_status("固件烧录成功")
                        return True
                    else:
                        self._update_status("固件烧录失败或超时")
                        return False
                except Exception as ymodem_error:
                    logger.error(f"Ymodem error: {ymodem_error}")
                    traceback.print_exc()
                    self._update_status(f"Ymodem传输错误: {str(ymodem_error)}")
                    return False
        except Exception as e:
            logger.error(f"Flash firmware with path error: {e}")
            self._update_status(f"烧录过程出错: {str(e)}")
            return False
    
    def _test_ymodem_readiness(self) -> bool:
        """测试设备是否准备好进行Ymodem传输 - 专门诊断正常模式切换问题，线程锁保护"""
        try:
            with self.serial_lock:
                if not self.serial_port or not self.serial_port.is_open:
                    logger.warning("串口未打开，无法测试Ymodem就绪性")
                    return False
                logger.info("开始Ymodem就绪性测试...")
                # 1. 清空所有缓冲区
                self.serial_port.reset_input_buffer()
                self.serial_port.reset_output_buffer()
                time.sleep(0.2)
                # 2. 发送测试触发命令
                test_trigger = "rb -E\r".encode('utf-8')
                self._update_status("发送测试Ymodem触发命令...")
                self.serial_port.write(test_trigger)
                time.sleep(0.5)
                # 3. 检查是否收到'C'响应
                test_timeout = 10.0  # 10秒测试超时
                start_time = time.time()
                while time.time() - start_time < test_timeout:
                    if self.serial_port.in_waiting > 0:
                        response = self.serial_port.read(self.serial_port.in_waiting)
                        logger.info(f"测试响应: {response}")
                        # 检查是否包含'C'字符
                        if b'C' in response or 0x43 in response:
                            self._update_status("设备Ymodem就绪性测试通过")
                            logger.info("设备响应了Ymodem触发命令，就绪性良好")
                            return True
                    time.sleep(0.1)
                # 4. 测试失败，但清空缓冲区为实际传输准备
                self._update_status("设备Ymodem就绪性测试超时，但继续尝试")
                logger.warning("设备未在测试中响应Ymodem触发命令")
                # 清空缓冲区，移除测试数据影响
                if self.serial_port.in_waiting > 0:
                    discarded = self.serial_port.read(self.serial_port.in_waiting)
                    logger.info(f"清空测试残留数据: {len(discarded)} 字节")
                return False
        except Exception as e:
            logger.error(f"Ymodem就绪性测试失败: {e}")
            self._update_status(f"就绪性测试异常: {str(e)}")
            return False
    
    def _verify_bootloader_stability(self) -> bool:
        """验证bootloader设备稳定性 - 彻底清理WPX协议残留数据，线程锁保护"""
        try:
            with self.serial_lock:
                if not self.serial_port or not self.serial_port.is_open:
                    logger.warning("串口未打开，无法验证稳定性")
                    return False
                
                # 2. 激进的多轮缓冲区清理
                total_cleared = 0
                for round_num in range(10):  # 增加到10轮清理
                    if self.serial_port.in_waiting > 0:
                        old_data = self.serial_port.read_all()
                        total_cleared += len(old_data)
                        logger.info(f"清除WPX残留数据第{round_num+1}轮: {len(old_data)} bytes (hex: {old_data.hex() if len(old_data) < 50 else old_data[:50].hex() + '...'})")
                        self._update_status(f"清理WPX残留数据 ({round_num+1}/10): {len(old_data)} bytes")
                    else:
                        logger.debug(f"缓冲区清理第{round_num+1}轮: 无残留数据")
                    time.sleep(0.5)  # 增加每轮间隔，给设备更多时间输出数据
                
                logger.info(f"总共清理了 {total_cleared} 字节的WPX残留数据")
                self._update_status(f"WPX数据清理完成，共清理 {total_cleared} 字节")
                
                # 2. 最终清理阶段 - 不发送任何命令，避免产生更多数据
                self._update_status("进行最终数据清理...")
                logger.info("开始最终清理阶段...")
                
                # 等待设备完全安静下来
                time.sleep(1.0)
                
                # 最后一轮清理
                if self.serial_port.in_waiting > 0:
                    final_cleanup = self.serial_port.read_all()
                    total_cleared += len(final_cleanup)
                    logger.info(f"最终清理: {len(final_cleanup)} bytes (hex: {final_cleanup.hex() if len(final_cleanup) < 50 else final_cleanup[:50].hex() + '...'})")
                    self._update_status(f"最终清理: {len(final_cleanup)} bytes")
                
                # 4. 绝对最终清理 - 确保进入Ymodem前完全干净
                if self.serial_port.in_waiting > 0:
                    absolute_final_data = self.serial_port.read_all()
                    total_cleared += len(absolute_final_data)
                    logger.info(f"绝对最终清理: {len(absolute_final_data)} bytes (hex: {absolute_final_data.hex() if len(absolute_final_data) < 50 else absolute_final_data[:50].hex() + '...'})")
                    self._update_status("完成绝对最终缓冲区清理")
                
                logger.info(f"Bootloader稳定性验证完成，总共清理了 {total_cleared} 字节WPX残留数据")
                # self._update_status(f"设备稳定性验证通过，已清理 {total_cleared} 字节残留数据，准备开始Ymodem")
                
                # 跳过波特率验证，避免产生更多通信数据
                logger.info("跳过波特率验证，避免干扰Ymodem通信")
                
                return True
                
                return True
        except Exception as e:
            logger.error(f"Bootloader稳定性验证失败: {e}")
            self._update_status(f"稳定性验证异常: {str(e)}")
            return False
    def _verify_bootloader_serial_config(self) -> bool:
        """验证bootloader串口配置 - 确保波特率正确"""
        try:
            if not self.serial_port or not self.serial_port.is_open:
                logger.warning("串口未打开，无法验证配置")
                return False
            
            current_baudrate = self.serial_port.baudrate
            expected_baudrate = 115200
            
            if current_baudrate != expected_baudrate:
                logger.warning(f"波特率不匹配! 当前: {current_baudrate}, 期望: {expected_baudrate}")
                self._update_status(f"警告: 波特率不匹配 ({current_baudrate} != {expected_baudrate})")
                
                # 尝试修正波特率
                logger.info("尝试修正波特率...")
                self.serial_port.baudrate = expected_baudrate
                time.sleep(0.2)
                logger.info(f"波特率已修正为: {self.serial_port.baudrate}")
                self._update_status("波特率已修正为115200")
            else:
                logger.info(f"波特率验证通过: {current_baudrate}")
                self._update_status(f"波特率验证通过: {current_baudrate}")
            
            return True
            
        except Exception as e:
            logger.error(f"波特率验证失败: {e}")
            self._update_status(f"波特率验证异常: {str(e)}")
            return False




class AutoKernelFlasher(AutoFirmwareFlasher):
    """自动内核烧录器 - 使用特定的内核固件URL和enter_bootloader_rx_cmd"""
    
    def __init__(self, serial_port, wpx_protocol: Optional[WPXProtocol]):
        super().__init__(serial_port, wpx_protocol)
        
        # 覆盖固件下载器设置为内核固件URL
        self.downloader.firmware_url = "http://ee-lab.cn/Firmware/firmwares/EELab_MPP_Pass.bin"
        logger.info("AutoKernelFlasher initialized with kernel firmware URL")
    
    def _send_wpx_data(self, data: bytes):
        """Send WPX protocol data via serial"""
        logger.info(f"_send_wpx_data called with {len(data)} bytes: {data.hex()}")
        if self.serial_port and self.serial_port.is_open:
            try:
                self.serial_port.write(data)
                logger.info(f"Successfully sent WPX data: {data.hex()}")
            except Exception as e:
                logger.error(f"Failed to send WPX data: {e}")
        else:
            logger.error(f"Cannot send WPX data: serial port not available (port: {self.serial_port}, is_open: {self.serial_port.is_open if self.serial_port else 'None'})")
    
    def start_auto_flash(self, force_download: bool = False) -> bool:
        """
        开始自动内核烧录流程
        """
        # 直接使用父类的start_auto_flash方法，它会调用我们重写的_enter_bootloader
        return super().start_auto_flash(force_download)
    
    def _enter_bootloader(self) -> bool:
        """进入Bootloader模式 - 使用内核专用的enter_bootloader_rx_cmd"""
        # 关键修复：内核更新时，设备可能从bootloader模式重启到正常模式
        # 需要重新检测设备状态，不能假设设备还在bootloader模式
        
        self._update_status("检查设备当前状态（正常模式/Bootloader模式）...")
        logger.info("Checking device current state for kernel update")
        
        # 首先检查是否有正常模式设备（系统固件更新完成后设备会重启到正常模式）
        normal_device_detected = False
        try:
            # 检查MainWindow.api状态来判断是否有正常模式连接
            # 通过globals()安全访问MainWindow
            main_window = globals().get('MainWindow')
            if main_window and hasattr(main_window, 'api') and main_window.api:
                if hasattr(main_window.api, '_ser') and main_window.api._ser and main_window.api._ser.is_open:
                    logger.info("Found normal mode device connection via MainWindow.api")
                    self._update_status("检测到正常模式设备，准备发送Fastboot命令...")
                    normal_device_detected = True
                    
                    # 使用现有的正常模式连接
                    self.serial_port = main_window.api._ser
                    self.wpx_protocol = main_window.api.wpx_protocol
                    logger.info(f"Using existing normal mode connection: {self.serial_port}")
            
            # 如果MainWindow.api不可用，尝试扫描正常模式设备
            if not normal_device_detected:
                logger.info("MainWindow.api not available, scanning for normal mode devices")
                try:
                    from controller import find_all_devices
                    normal_devices = find_all_devices()
                    
                    if normal_devices:
                        logger.info(f"Found normal mode devices: {normal_devices}")
                        self._update_status(f"检测到正常模式设备: {normal_devices[0][0]}")
                        normal_device_detected = True
                        
                        # 连接到正常模式设备
                        port, description = normal_devices[0]
                        success = self._safe_serial_connect(port, 921600, "正常模式设备")
                        if success:
                            logger.info(f"Successfully connected to normal mode device: {port}")
                            # 需要初始化WPX协议
                            try:
                                from controller.wpx_protocol import WPXProtocol
                                self.wpx_protocol = WPXProtocol()
                                # 设置数据发送回调
                                self.wpx_protocol.set_send_data_callback(self._send_wpx_data)
                                logger.info("WPX protocol initialized for normal mode device")
                                # 连接和WPX协议初始化都成功，确认检测到正常模式设备
                                normal_device_detected = True
                            except Exception as wpx_error:
                                logger.error(f"Failed to initialize WPX protocol: {wpx_error}")
                                self.wpx_protocol = None
                                # WPX协议初始化失败，但串口连接成功，仍然可以尝试
                                normal_device_detected = True
                        else:
                            logger.error("Failed to connect to normal mode device")
                            normal_device_detected = False
                    else:
                        logger.info("No normal mode devices found")
                        
                except Exception as scan_error:
                    logger.warning(f"Error scanning for normal mode devices: {scan_error}")
                    
        except Exception as check_error:
            logger.warning(f"Error checking for normal mode devices: {check_error}")
        
        # 如果检测到正常模式设备，发送Fastboot命令
        if normal_device_detected:
            logger.info(f"Normal device detected, wpx_protocol available: {self.wpx_protocol is not None}")
            
            if self.wpx_protocol is not None:
                try:
                    self._update_status("发送进入Fastboot命令（内核模式）...")
                    logger.info("Sending kernel fastboot command to normal mode device")
                    
                    # 增强的内核命令发送重试机制
                    command_sent = False
                    for cmd_attempt in range(3):  # 内核模式尝试3次（更重要）
                        try:
                            if cmd_attempt > 0:
                                logger.info(f"Retrying kernel fastboot command send (attempt {cmd_attempt + 1})")
                                self._update_status(f"重试发送内核Fastboot命令 (第{cmd_attempt + 1}次)...")
                                
                                # 更彻底的连接恢复
                                if self.serial_port and self.serial_port.is_open:
                                    # 清理缓冲区
                                    self.serial_port.reset_input_buffer()
                                    self.serial_port.reset_output_buffer()
                                    time.sleep(0.2)
                            
                            # 使用内核专用的enter_fastboot_cmd
                            logger.info(f"Attempting to send fastboot command (attempt {cmd_attempt + 1})")
                            self.wpx_protocol.enter_fastboot_cmd()
                            time.sleep(1.0)  # 给设备更多时间切换到bootloader模式
                            command_sent = True
                            logger.info("Kernel fastboot command sent successfully")
                            break  # 成功发送，退出重试循环
                            
                        except Exception as retry_error:
                            logger.warning(f"Kernel fastboot command attempt {cmd_attempt + 1} failed: {retry_error}")
                            if cmd_attempt == 2:  # 最后一次尝试
                                raise retry_error
                    
                    if command_sent:
                        # 关闭正常模式连接，等待设备进入bootloader模式
                        try:
                            if self.serial_port and self.serial_port.is_open:
                                self.serial_port.close()
                                logger.info("Closed normal mode connection after fastboot command")
                            time.sleep(1.0)  # 等待设备切换
                        except Exception as close_error:
                            logger.warning(f"Error closing normal mode connection: {close_error}")
                        
                        # 等待设备进入bootloader模式
                        return self._wait_for_new_bootloader_device(True)
                    else:
                        logger.error("Failed to send kernel fastboot command after retries")
                        self._update_status("发送Fastboot命令失败")
                        return False
                        
                except Exception as cmd_error:
                    logger.warning(f"All kernel fastboot command attempts failed: {cmd_error}")
                    self._update_status("命令发送失败，但继续检测Bootloader设备...")
                    return self._wait_for_new_bootloader_device(False)
            else:
                logger.warning("Normal device detected but WPX protocol is None")
                self._update_status("检测到正常模式设备但WPX协议不可用，尝试检测Bootloader设备...")
                # 没有WPX协议，但可能设备会自动进入bootloader模式，尝试等待
                return self._wait_for_new_bootloader_device(False)
        
        # 如果没有检测到正常模式设备，则检查是否已经存在bootloader设备
        self._update_status("检查是否已存在Bootloader设备...")
        logger.info("No normal mode device detected, checking for existing bootloader devices")
        
        try:
            from controller import find_bootloader_devices
            existing_bootloader_devices = find_bootloader_devices()
            
            if existing_bootloader_devices:
                logger.info(f"Found existing bootloader devices: {existing_bootloader_devices}")
                # 如果已存在bootloader设备，直接连接
                return self._connect_to_existing_bootloader(existing_bootloader_devices)
            else:
                logger.warning("No bootloader devices found")
                self._update_status("未找到Bootloader设备进行重连")
                return False
                
        except Exception as check_error:
            logger.warning(f"Error checking for existing bootloader devices: {check_error}")
            self._update_status("检测Bootloader设备时出错")
            return False
    
    def _connect_to_existing_bootloader(self, existing_bootloader_devices):
        """连接到已存在的bootloader设备"""
        if len(existing_bootloader_devices) == 1:
            bootloader_port = existing_bootloader_devices[0][0]
            self._update_status(f"发现已存在的Bootloader设备: {bootloader_port}")
            logger.info(f"Found existing bootloader device: {bootloader_port}")
        else:
            # 多个bootloader设备，让用户选择
            if self.multi_device_callback:
                bootloader_port = self.multi_device_callback(existing_bootloader_devices)
                if not bootloader_port:
                    self._update_status("用户取消了设备选择")
                    return False
                self._update_status(f"用户选择了Bootloader设备: {bootloader_port}")
            else:
                # 没有回调，使用第一个
                bootloader_port = existing_bootloader_devices[0][0]
                self._update_status(f"使用第一个Bootloader设备: {bootloader_port}")
        
        # 连接到bootloader设备
        success = self._safe_serial_connect(bootloader_port, 115200, "现有Bootloader设备")
        if success:
            logger.info(f"Successfully connected to existing bootloader device: {bootloader_port}")
            return True
        else:
            self._update_status(f"连接现有Bootloader设备失败")
            return False
    
    def _wait_for_new_bootloader_device(self, command_sent: bool):
        """等待新的bootloader设备出现"""
        self._update_status("等待设备进入Bootloader模式...")
        logger.info(f"Waiting for bootloader device to appear (command_sent: {command_sent})")
        
        try:
            import threading
            
            bootloader_port = None
            detection_complete = threading.Event()
            
            def detect_bootloader():
                nonlocal bootloader_port
                try:
                    from controller import wait_for_bootloader_device_with_callback
                    bootloader_port = wait_for_bootloader_device_with_callback(
                        timeout=8.0,
                        check_interval=0.3,
                        multi_device_callback=self.multi_device_callback
                    )
                    logger.info(f"Bootloader detection completed: {bootloader_port}")
                except Exception as e:
                    logger.error(f"Bootloader detection error: {e}")
                    bootloader_port = None
                finally:
                    detection_complete.set()
            
            detection_thread = threading.Thread(target=detect_bootloader)
            detection_thread.daemon = True
            detection_thread.start()
            
            if detection_complete.wait(timeout=10.0):
                logger.info(f"Bootloader detection finished: {bootloader_port}")
            else:
                logger.warning("Bootloader detection timeout after 10 seconds")
                bootloader_port = None
                
        except Exception as detection_error:
            logger.error(f"Bootloader detection setup error: {detection_error}")
            bootloader_port = None
        
        # 连接到检测到的设备
        if bootloader_port:
            success = self._safe_serial_connect(bootloader_port, 115200, "Bootloader设备")
            if success:
                logger.info(f"Successfully connected to bootloader (command_sent: {command_sent})")
                
                # 关键修复：新检测到的bootloader设备也需要稳定等待
                logger.info("新bootloader设备连接成功，开始稳定性检测...")
                self._update_status("新设备已连接，正在验证bootloader稳定性...")
                
                if self._verify_bootloader_stability():
                    logger.info("新bootloader设备稳定性验证通过")
                else:
                    logger.warning("新bootloader设备稳定性验证失败，但继续尝试")
                
                return True
            else:
                self._update_status(f"连接Bootloader设备失败")
                return False
        else:
            if command_sent:
                self._update_status("超时: 未检测到Bootloader设备")
                logger.warning("Kernel bootloader command was sent but no device appeared")
            else:
                self._update_status("未检测到Bootloader设备（设备可能已在Bootloader模式但连接异常）")
                logger.warning("Device should be in bootloader mode but connection/detection failed")
            logger.warning(f"No bootloader device detected (command_sent: {command_sent})")
            return False