#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字体混叠修复脚本
修复Windows系统上PingFang字体导致的混叠问题
将UI文件中的PingFang字体替换为Microsoft YaHei
"""

import os
import re
import shutil
import sys
from pathlib import Path


def backup_file(file_path):
    """备份文件"""
    backup_path = f"{file_path}.backup"
    if not os.path.exists(backup_path):
        shutil.copy2(file_path, backup_path)
        print(f"已备份: {file_path} -> {backup_path}")
    else:
        print(f"备份已存在: {backup_path}")

def fix_ui_file(file_path):
    """修复UI文件中的字体设置"""
    print(f"正在修复UI文件: {file_path}")

    # 备份原文件
    backup_file(file_path)

    # 读取文件内容
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()

    original_content = content

    # 替换PingFang为Microsoft YaHei
    # 匹配 <family>PingFang</family> 模式
    content = re.sub(
        r'<family>PingFang</family>',
        '<family>Microsoft YaHei</family>',
        content
    )

    # 统计替换次数
    replacements = content.count('<family>Microsoft YaHei</family>')
    print(f"  替换了 {replacements} 处PingFang字体")

    # 写回文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)

    print(f"已修复UI文件: {file_path}")

def fix_python_ui_file(file_path):
    """修复Python UI文件中的字体设置"""
    print(f"正在修复Python UI文件: {file_path}")

    # 备份原文件
    backup_file(file_path)

    # 读取文件内容
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()

    original_content = content

    # 替换 font.setFamily("PingFang") 为 font.setFamily("Microsoft YaHei")
    content = re.sub(
        r'font\.setFamily\("PingFang"\)',
        'font.setFamily("Microsoft YaHei")',
        content
    )

    # 统计替换次数
    original_count = original_content.count('font.setFamily("PingFang")')
    new_count = content.count('font.setFamily("Microsoft YaHei")')
    print(f"  替换了 {original_count} 处PingFang字体设置")

    # 写回文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)

    print(f"已修复Python UI文件: {file_path}")

def main():
    """主函数"""
    print("开始修复字体混叠问题...")
    print("直接修复Python UI文件中的PingFang字体以避免Windows系统上的混叠问题")

    # 获取脚本所在目录
    script_dir = Path(__file__).parent
    template_dir = script_dir / "mdp_gui_template"

    # 需要修复的Python UI文件
    python_ui_files = [
        template_dir / "mainwindow_ui.py",
        template_dir / "settings_ui.py",
        template_dir / "updates_ui.py",
        template_dir / "graphics_ui.py",
        template_dir / "applestyledisplay.py"
    ]

    # 修复Python UI文件
    for py_file in python_ui_files:
        if py_file.exists():
            fix_python_ui_file(str(py_file))
        else:
            print(f"文件不存在，跳过: {py_file}")

    print("\n字体混叠修复完成！")
    print("已直接修复Python UI文件，无需重新编译")

if __name__ == "__main__":
    main()
