# echo "> Building WPX-01"

# .venv\Scripts\pyinstaller.exe --noconfirm --upx-dir C:\Toolchains\upx .\mdp.spec

# echo "> Building WPX-01-Numba"

# .venv\Scripts\pyinstaller.exe --noconfirm --upx-dir C:\Toolchains\upx .\mdp_numba.spec

echo "> Building WPX-01 and WPX-01-Numba in parallel"
# Start both builds in parallel
$job1 = Start-Job -ScriptBlock {
    Set-Location $using:PWD
    .\.venv\Scripts\pyinstaller.exe --noconfirm .\mdp.spec --log-level WARN
}

$job2 = Start-Job -ScriptBlock {
    Set-Location $using:PWD
    .\.venv\Scripts\pyinstaller.exe --noconfirm .\mdp_numba.spec --log-level WARN
}

# Wait for both jobs to complete
Wait-Job $job1, $job2

# Get output from jobs
echo "> WPX-01 build output:"
Receive-Job $job1
echo "> WPX-01-Numba build output:"
Receive-Job $job2

# Clean up
Remove-Job $job1, $job2

echo "> Build completed"
