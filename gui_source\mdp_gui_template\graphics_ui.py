# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'mdp_gui_template\graphics.ui'
#
# Created by: PyQt5 UI code generator 5.15.11
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_DialogGraphics(object):
    def setupUi(self, DialogGraphics):
        DialogGraphics.setObjectName("DialogGraphics")
        DialogGraphics.setWindowModality(QtCore.Qt.WindowModal)
        DialogGraphics.setEnabled(True)
        DialogGraphics.resize(280, 640)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(DialogGraphics.sizePolicy().hasHeightForWidth())
        DialogGraphics.setSizePolicy(sizePolicy)
        DialogGraphics.setMinimumSize(QtCore.QSize(280, 640))
        DialogGraphics.setMaximumSize(QtCore.QSize(280, 640))
        DialogGraphics.setSizeGripEnabled(False)
        DialogGraphics.setModal(True)
        self.verticalLayout = QtWidgets.QVBoxLayout(DialogGraphics)
        self.verticalLayout.setSpacing(2)
        self.verticalLayout.setObjectName("verticalLayout")
        self.label = QtWidgets.QLabel(DialogGraphics)
        self.label.setMaximumSize(QtCore.QSize(16777215, 20))
        self.label.setText("")
        self.label.setObjectName("label")
        self.verticalLayout.addWidget(self.label)
        self.label_46 = QtWidgets.QLabel(DialogGraphics)
        self.label_46.setMinimumSize(QtCore.QSize(0, 28))
        self.label_46.setMaximumSize(QtCore.QSize(999, 16777215))
        font = QtGui.QFont()
        font.setFamily("PingFang")
        font.setBold(True)
        self.label_46.setFont(font)
        self.label_46.setAlignment(QtCore.Qt.AlignCenter)
        self.label_46.setObjectName("label_46")
        self.verticalLayout.addWidget(self.label_46)
        self.horizontalLayout_7 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_7.setSpacing(6)
        self.horizontalLayout_7.setObjectName("horizontalLayout_7")
        self.label_36 = QtWidgets.QLabel(DialogGraphics)
        self.label_36.setMinimumSize(QtCore.QSize(0, 24))
        self.label_36.setMaximumSize(QtCore.QSize(999, 16777215))
        font = QtGui.QFont()
        font.setFamily("PingFang")
        font.setBold(True)
        self.label_36.setFont(font)
        self.label_36.setAlignment(QtCore.Qt.AlignCenter)
        self.label_36.setObjectName("label_36")
        self.horizontalLayout_7.addWidget(self.label_36)
        self.label_41 = QtWidgets.QLabel(DialogGraphics)
        self.label_41.setMinimumSize(QtCore.QSize(0, 24))
        self.label_41.setMaximumSize(QtCore.QSize(999, 16777215))
        font = QtGui.QFont()
        font.setFamily("PingFang")
        font.setBold(True)
        self.label_41.setFont(font)
        self.label_41.setAlignment(QtCore.Qt.AlignCenter)
        self.label_41.setObjectName("label_41")
        self.horizontalLayout_7.addWidget(self.label_41)
        self.verticalLayout.addLayout(self.horizontalLayout_7)
        self.horizontalLayout_6 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_6.setSpacing(6)
        self.horizontalLayout_6.setObjectName("horizontalLayout_6")
        self.comboTheme = QtWidgets.QComboBox(DialogGraphics)
        font = QtGui.QFont()
        font.setFamily("PingFang")
        font.setBold(True)
        self.comboTheme.setFont(font)
        self.comboTheme.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.comboTheme.setEditable(False)
        self.comboTheme.setSizeAdjustPolicy(QtWidgets.QComboBox.AdjustToContentsOnFirstShow)
        self.comboTheme.setObjectName("comboTheme")
        self.comboTheme.addItem("")
        self.comboTheme.addItem("")
        self.horizontalLayout_6.addWidget(self.comboTheme)
        self.comboInput = QtWidgets.QComboBox(DialogGraphics)
        font = QtGui.QFont()
        font.setFamily("PingFang")
        font.setBold(True)
        self.comboInput.setFont(font)
        self.comboInput.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.comboInput.setEditable(False)
        self.comboInput.setSizeAdjustPolicy(QtWidgets.QComboBox.AdjustToContentsOnFirstShow)
        self.comboInput.setObjectName("comboInput")
        self.comboInput.addItem("")
        self.comboInput.addItem("")
        self.horizontalLayout_6.addWidget(self.comboInput)
        self.verticalLayout.addLayout(self.horizontalLayout_6)
        self.line_3 = QtWidgets.QFrame(DialogGraphics)
        self.line_3.setFrameShape(QtWidgets.QFrame.HLine)
        self.line_3.setFrameShadow(QtWidgets.QFrame.Sunken)
        self.line_3.setObjectName("line_3")
        self.verticalLayout.addWidget(self.line_3)
        self.label_35 = QtWidgets.QLabel(DialogGraphics)
        self.label_35.setMinimumSize(QtCore.QSize(0, 28))
        self.label_35.setMaximumSize(QtCore.QSize(999, 16777215))
        font = QtGui.QFont()
        font.setFamily("PingFang")
        font.setBold(True)
        self.label_35.setFont(font)
        self.label_35.setAlignment(QtCore.Qt.AlignCenter)
        self.label_35.setObjectName("label_35")
        self.verticalLayout.addWidget(self.label_35)
        self.horizontalLayout_13 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_13.setSpacing(6)
        self.horizontalLayout_13.setObjectName("horizontalLayout_13")
        self.label_39 = QtWidgets.QLabel(DialogGraphics)
        self.label_39.setMinimumSize(QtCore.QSize(0, 24))
        self.label_39.setMaximumSize(QtCore.QSize(999, 16777215))
        font = QtGui.QFont()
        font.setFamily("PingFang")
        font.setBold(True)
        self.label_39.setFont(font)
        self.label_39.setAlignment(QtCore.Qt.AlignCenter)
        self.label_39.setObjectName("label_39")
        self.horizontalLayout_13.addWidget(self.label_39)
        self.verticalLayout.addLayout(self.horizontalLayout_13)
        self.horizontalLayout_12 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_12.setSpacing(6)
        self.horizontalLayout_12.setObjectName("horizontalLayout_12")
        self.spinMaxFps = QtWidgets.QDoubleSpinBox(DialogGraphics)
        font = QtGui.QFont()
        font.setFamily("PingFang")
        font.setBold(True)
        self.spinMaxFps.setFont(font)
        self.spinMaxFps.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.spinMaxFps.setDecimals(1)
        self.spinMaxFps.setMinimum(1.0)
        self.spinMaxFps.setMaximum(120.0)
        self.spinMaxFps.setSingleStep(5.0)
        self.spinMaxFps.setProperty("value", 60.0)
        self.spinMaxFps.setObjectName("spinMaxFps")
        self.horizontalLayout_12.addWidget(self.spinMaxFps)
        self.verticalLayout.addLayout(self.horizontalLayout_12)
        self.horizontalLayout_14 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_14.setSpacing(6)
        self.horizontalLayout_14.setObjectName("horizontalLayout_14")
        self.label_40 = QtWidgets.QLabel(DialogGraphics)
        self.label_40.setMinimumSize(QtCore.QSize(0, 24))
        self.label_40.setMaximumSize(QtCore.QSize(999, 16777215))
        font = QtGui.QFont()
        font.setFamily("PingFang")
        font.setBold(True)
        self.label_40.setFont(font)
        self.label_40.setAlignment(QtCore.Qt.AlignCenter)
        self.label_40.setObjectName("label_40")
        self.horizontalLayout_14.addWidget(self.label_40)
        self.label_47 = QtWidgets.QLabel(DialogGraphics)
        self.label_47.setMinimumSize(QtCore.QSize(0, 24))
        self.label_47.setMaximumSize(QtCore.QSize(999, 16777215))
        font = QtGui.QFont()
        font.setFamily("PingFang")
        font.setBold(True)
        self.label_47.setFont(font)
        self.label_47.setAlignment(QtCore.Qt.AlignCenter)
        self.label_47.setObjectName("label_47")
        self.horizontalLayout_14.addWidget(self.label_47)
        self.verticalLayout.addLayout(self.horizontalLayout_14)
        self.horizontalLayout_15 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_15.setSpacing(6)
        self.horizontalLayout_15.setObjectName("horizontalLayout_15")
        self.spinDataLength = QtWidgets.QSpinBox(DialogGraphics)
        font = QtGui.QFont()
        font.setFamily("PingFang")
        font.setBold(True)
        self.spinDataLength.setFont(font)
        self.spinDataLength.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.spinDataLength.setPrefix("")
        self.spinDataLength.setMinimum(100)
        self.spinDataLength.setMaximum(999999999)
        self.spinDataLength.setSingleStep(100)
        self.spinDataLength.setProperty("value", 500)
        self.spinDataLength.setObjectName("spinDataLength")
        self.horizontalLayout_15.addWidget(self.spinDataLength)
        self.spinDisplayLength = QtWidgets.QSpinBox(DialogGraphics)
        font = QtGui.QFont()
        font.setFamily("PingFang")
        font.setBold(True)
        self.spinDisplayLength.setFont(font)
        self.spinDisplayLength.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.spinDisplayLength.setPrefix("")
        self.spinDisplayLength.setMinimum(100)
        self.spinDisplayLength.setMaximum(999999999)
        self.spinDisplayLength.setSingleStep(100)
        self.spinDisplayLength.setProperty("value", 500)
        self.spinDisplayLength.setObjectName("spinDisplayLength")
        self.horizontalLayout_15.addWidget(self.spinDisplayLength)
        self.verticalLayout.addLayout(self.horizontalLayout_15)
        self.labelNumba = QtWidgets.QLabel(DialogGraphics)
        self.labelNumba.setMinimumSize(QtCore.QSize(0, 24))
        self.labelNumba.setMaximumSize(QtCore.QSize(999, 16777215))
        font = QtGui.QFont()
        font.setFamily("PingFang")
        font.setBold(True)
        self.labelNumba.setFont(font)
        self.labelNumba.setAlignment(QtCore.Qt.AlignCenter)
        self.labelNumba.setObjectName("labelNumba")
        self.verticalLayout.addWidget(self.labelNumba)
        self.horizontalLayout_5 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_5.setSpacing(6)
        self.horizontalLayout_5.setObjectName("horizontalLayout_5")
        self.checkBoxOpenGL = QtWidgets.QCheckBox(DialogGraphics)
        font = QtGui.QFont()
        font.setFamily("PingFang")
        font.setBold(True)
        self.checkBoxOpenGL.setFont(font)
        self.checkBoxOpenGL.setObjectName("checkBoxOpenGL")
        self.horizontalLayout_5.addWidget(self.checkBoxOpenGL)
        self.checkBoxAntialias = QtWidgets.QCheckBox(DialogGraphics)
        font = QtGui.QFont()
        font.setFamily("PingFang")
        font.setBold(True)
        self.checkBoxAntialias.setFont(font)
        self.checkBoxAntialias.setObjectName("checkBoxAntialias")
        self.horizontalLayout_5.addWidget(self.checkBoxAntialias)
        self.verticalLayout.addLayout(self.horizontalLayout_5)
        self.line = QtWidgets.QFrame(DialogGraphics)
        self.line.setFrameShape(QtWidgets.QFrame.HLine)
        self.line.setFrameShadow(QtWidgets.QFrame.Sunken)
        self.line.setObjectName("line")
        self.verticalLayout.addWidget(self.line)
        self.label_38 = QtWidgets.QLabel(DialogGraphics)
        self.label_38.setMinimumSize(QtCore.QSize(0, 28))
        self.label_38.setMaximumSize(QtCore.QSize(999, 16777215))
        font = QtGui.QFont()
        font.setFamily("PingFang")
        font.setBold(True)
        self.label_38.setFont(font)
        self.label_38.setAlignment(QtCore.Qt.AlignCenter)
        self.label_38.setObjectName("label_38")
        self.verticalLayout.addWidget(self.label_38)
        self.horizontalLayout_11 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_11.setSpacing(6)
        self.horizontalLayout_11.setObjectName("horizontalLayout_11")
        self.label_43 = QtWidgets.QLabel(DialogGraphics)
        self.label_43.setMinimumSize(QtCore.QSize(0, 24))
        self.label_43.setMaximumSize(QtCore.QSize(999, 16777215))
        font = QtGui.QFont()
        font.setFamily("PingFang")
        font.setBold(True)
        self.label_43.setFont(font)
        self.label_43.setAlignment(QtCore.Qt.AlignCenter)
        self.label_43.setObjectName("label_43")
        self.horizontalLayout_11.addWidget(self.label_43)
        self.label_44 = QtWidgets.QLabel(DialogGraphics)
        self.label_44.setMinimumSize(QtCore.QSize(0, 24))
        self.label_44.setMaximumSize(QtCore.QSize(999, 16777215))
        font = QtGui.QFont()
        font.setFamily("PingFang")
        font.setBold(True)
        self.label_44.setFont(font)
        self.label_44.setAlignment(QtCore.Qt.AlignCenter)
        self.label_44.setObjectName("label_44")
        self.horizontalLayout_11.addWidget(self.label_44)
        self.verticalLayout.addLayout(self.horizontalLayout_11)
        self.horizontalLayout_10 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_10.setSpacing(6)
        self.horizontalLayout_10.setObjectName("horizontalLayout_10")
        self.spinStateFps = QtWidgets.QDoubleSpinBox(DialogGraphics)
        font = QtGui.QFont()
        font.setFamily("PingFang")
        font.setBold(True)
        self.spinStateFps.setFont(font)
        self.spinStateFps.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.spinStateFps.setDecimals(1)
        self.spinStateFps.setMinimum(1.0)
        self.spinStateFps.setMaximum(120.0)
        self.spinStateFps.setSingleStep(5.0)
        self.spinStateFps.setProperty("value", 60.0)
        self.spinStateFps.setObjectName("spinStateFps")
        self.horizontalLayout_10.addWidget(self.spinStateFps)
        self.comboInterp = QtWidgets.QComboBox(DialogGraphics)
        font = QtGui.QFont()
        font.setFamily("PingFang")
        font.setBold(True)
        self.comboInterp.setFont(font)
        self.comboInterp.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.comboInterp.setEditable(False)
        self.comboInterp.setSizeAdjustPolicy(QtWidgets.QComboBox.AdjustToContentsOnFirstShow)
        self.comboInterp.setObjectName("comboInterp")
        self.comboInterp.addItem("")
        self.comboInterp.addItem("")
        self.comboInterp.addItem("")
        self.comboInterp.addItem("")
        self.horizontalLayout_10.addWidget(self.comboInterp)
        self.verticalLayout.addLayout(self.horizontalLayout_10)
        self.line_2 = QtWidgets.QFrame(DialogGraphics)
        self.line_2.setFrameShape(QtWidgets.QFrame.HLine)
        self.line_2.setFrameShadow(QtWidgets.QFrame.Sunken)
        self.line_2.setObjectName("line_2")
        self.verticalLayout.addWidget(self.line_2)
        self.label_42 = QtWidgets.QLabel(DialogGraphics)
        self.label_42.setMinimumSize(QtCore.QSize(0, 28))
        self.label_42.setMaximumSize(QtCore.QSize(999, 16777215))
        font = QtGui.QFont()
        font.setFamily("PingFang")
        font.setBold(True)
        self.label_42.setFont(font)
        self.label_42.setAlignment(QtCore.Qt.AlignCenter)
        self.label_42.setObjectName("label_42")
        self.verticalLayout.addWidget(self.label_42)
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_2.setSpacing(6)
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.spinStateVThres = QtWidgets.QDoubleSpinBox(DialogGraphics)
        font = QtGui.QFont()
        font.setFamily("PingFang")
        font.setBold(True)
        self.spinStateVThres.setFont(font)
        self.spinStateVThres.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.spinStateVThres.setDecimals(3)
        self.spinStateVThres.setMinimum(0.0)
        self.spinStateVThres.setMaximum(1.0)
        self.spinStateVThres.setSingleStep(0.001)
        self.spinStateVThres.setProperty("value", 0.0)
        self.spinStateVThres.setObjectName("spinStateVThres")
        self.horizontalLayout_2.addWidget(self.spinStateVThres)
        self.spinStateIThres = QtWidgets.QDoubleSpinBox(DialogGraphics)
        font = QtGui.QFont()
        font.setFamily("PingFang")
        font.setBold(True)
        self.spinStateIThres.setFont(font)
        self.spinStateIThres.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.spinStateIThres.setDecimals(3)
        self.spinStateIThres.setMinimum(0.0)
        self.spinStateIThres.setMaximum(1.0)
        self.spinStateIThres.setSingleStep(0.001)
        self.spinStateIThres.setProperty("value", 0.0)
        self.spinStateIThres.setObjectName("spinStateIThres")
        self.horizontalLayout_2.addWidget(self.spinStateIThres)
        self.verticalLayout.addLayout(self.horizontalLayout_2)
        self.line_4 = QtWidgets.QFrame(DialogGraphics)
        self.line_4.setFrameShape(QtWidgets.QFrame.HLine)
        self.line_4.setFrameShadow(QtWidgets.QFrame.Sunken)
        self.line_4.setObjectName("line_4")
        self.verticalLayout.addWidget(self.line_4)
        self.labelCali_2 = QtWidgets.QLabel(DialogGraphics)
        self.labelCali_2.setMinimumSize(QtCore.QSize(0, 24))
        self.labelCali_2.setMaximumSize(QtCore.QSize(999, 16777215))
        font = QtGui.QFont()
        font.setFamily("PingFang")
        font.setBold(True)
        self.labelCali_2.setFont(font)
        self.labelCali_2.setAlignment(QtCore.Qt.AlignCenter)
        self.labelCali_2.setObjectName("labelCali_2")
        self.verticalLayout.addWidget(self.labelCali_2)
        self.checkBoxUseCali = QtWidgets.QCheckBox(DialogGraphics)
        font = QtGui.QFont()
        font.setFamily("PingFang")
        font.setBold(True)
        self.checkBoxUseCali.setFont(font)
        self.checkBoxUseCali.setObjectName("checkBoxUseCali")
        self.verticalLayout.addWidget(self.checkBoxUseCali)
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_3.setSpacing(6)
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        self.spinCaliVk = QtWidgets.QDoubleSpinBox(DialogGraphics)
        font = QtGui.QFont()
        font.setFamily("PingFang")
        font.setBold(True)
        self.spinCaliVk.setFont(font)
        self.spinCaliVk.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.spinCaliVk.setSuffix("")
        self.spinCaliVk.setDecimals(8)
        self.spinCaliVk.setMinimum(-100.0)
        self.spinCaliVk.setMaximum(100.0)
        self.spinCaliVk.setSingleStep(0.001)
        self.spinCaliVk.setProperty("value", 1.0)
        self.spinCaliVk.setObjectName("spinCaliVk")
        self.horizontalLayout_3.addWidget(self.spinCaliVk)
        self.spinCaliVb = QtWidgets.QDoubleSpinBox(DialogGraphics)
        font = QtGui.QFont()
        font.setFamily("PingFang")
        font.setBold(True)
        self.spinCaliVb.setFont(font)
        self.spinCaliVb.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.spinCaliVb.setSuffix("")
        self.spinCaliVb.setDecimals(8)
        self.spinCaliVb.setMinimum(-100.0)
        self.spinCaliVb.setMaximum(100.0)
        self.spinCaliVb.setSingleStep(0.001)
        self.spinCaliVb.setProperty("value", 0.0)
        self.spinCaliVb.setObjectName("spinCaliVb")
        self.horizontalLayout_3.addWidget(self.spinCaliVb)
        self.verticalLayout.addLayout(self.horizontalLayout_3)
        self.horizontalLayout_4 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_4.setSpacing(6)
        self.horizontalLayout_4.setObjectName("horizontalLayout_4")
        self.spinCaliIk = QtWidgets.QDoubleSpinBox(DialogGraphics)
        font = QtGui.QFont()
        font.setFamily("PingFang")
        font.setBold(True)
        self.spinCaliIk.setFont(font)
        self.spinCaliIk.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.spinCaliIk.setSuffix("")
        self.spinCaliIk.setDecimals(8)
        self.spinCaliIk.setMinimum(-100.0)
        self.spinCaliIk.setMaximum(100.0)
        self.spinCaliIk.setSingleStep(0.001)
        self.spinCaliIk.setProperty("value", 1.0)
        self.spinCaliIk.setObjectName("spinCaliIk")
        self.horizontalLayout_4.addWidget(self.spinCaliIk)
        self.spinCaliIb = QtWidgets.QDoubleSpinBox(DialogGraphics)
        font = QtGui.QFont()
        font.setFamily("PingFang")
        font.setBold(True)
        self.spinCaliIb.setFont(font)
        self.spinCaliIb.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.spinCaliIb.setSuffix("")
        self.spinCaliIb.setDecimals(8)
        self.spinCaliIb.setMinimum(-100.0)
        self.spinCaliIb.setMaximum(100.0)
        self.spinCaliIb.setSingleStep(0.001)
        self.spinCaliIb.setProperty("value", 0.0)
        self.spinCaliIb.setObjectName("spinCaliIb")
        self.horizontalLayout_4.addWidget(self.spinCaliIb)
        self.verticalLayout.addLayout(self.horizontalLayout_4)
        self.horizontalLayout_8 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_8.setSpacing(6)
        self.horizontalLayout_8.setObjectName("horizontalLayout_8")
        self.spinCaliVk_2 = QtWidgets.QDoubleSpinBox(DialogGraphics)
        font = QtGui.QFont()
        font.setFamily("PingFang")
        font.setBold(True)
        self.spinCaliVk_2.setFont(font)
        self.spinCaliVk_2.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.spinCaliVk_2.setSuffix("")
        self.spinCaliVk_2.setDecimals(8)
        self.spinCaliVk_2.setMinimum(-100.0)
        self.spinCaliVk_2.setMaximum(100.0)
        self.spinCaliVk_2.setSingleStep(0.001)
        self.spinCaliVk_2.setProperty("value", 1.0)
        self.spinCaliVk_2.setObjectName("spinCaliVk_2")
        self.horizontalLayout_8.addWidget(self.spinCaliVk_2)
        self.spinCaliVb_2 = QtWidgets.QDoubleSpinBox(DialogGraphics)
        font = QtGui.QFont()
        font.setFamily("PingFang")
        font.setBold(True)
        self.spinCaliVb_2.setFont(font)
        self.spinCaliVb_2.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.spinCaliVb_2.setSuffix("")
        self.spinCaliVb_2.setDecimals(8)
        self.spinCaliVb_2.setMinimum(-100.0)
        self.spinCaliVb_2.setMaximum(100.0)
        self.spinCaliVb_2.setSingleStep(0.001)
        self.spinCaliVb_2.setProperty("value", 0.0)
        self.spinCaliVb_2.setObjectName("spinCaliVb_2")
        self.horizontalLayout_8.addWidget(self.spinCaliVb_2)
        self.verticalLayout.addLayout(self.horizontalLayout_8)
        self.horizontalLayout_9 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_9.setSpacing(6)
        self.horizontalLayout_9.setObjectName("horizontalLayout_9")
        self.spinCaliIk_2 = QtWidgets.QDoubleSpinBox(DialogGraphics)
        font = QtGui.QFont()
        font.setFamily("PingFang")
        font.setBold(True)
        self.spinCaliIk_2.setFont(font)
        self.spinCaliIk_2.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.spinCaliIk_2.setSuffix("")
        self.spinCaliIk_2.setDecimals(8)
        self.spinCaliIk_2.setMinimum(-100.0)
        self.spinCaliIk_2.setMaximum(100.0)
        self.spinCaliIk_2.setSingleStep(0.001)
        self.spinCaliIk_2.setProperty("value", 1.0)
        self.spinCaliIk_2.setObjectName("spinCaliIk_2")
        self.horizontalLayout_9.addWidget(self.spinCaliIk_2)
        self.spinCaliIb_2 = QtWidgets.QDoubleSpinBox(DialogGraphics)
        font = QtGui.QFont()
        font.setFamily("PingFang")
        font.setBold(True)
        self.spinCaliIb_2.setFont(font)
        self.spinCaliIb_2.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.spinCaliIb_2.setSuffix("")
        self.spinCaliIb_2.setDecimals(8)
        self.spinCaliIb_2.setMinimum(-100.0)
        self.spinCaliIb_2.setMaximum(100.0)
        self.spinCaliIb_2.setSingleStep(0.001)
        self.spinCaliIb_2.setProperty("value", 0.0)
        self.spinCaliIb_2.setObjectName("spinCaliIb_2")
        self.horizontalLayout_9.addWidget(self.spinCaliIb_2)
        self.verticalLayout.addLayout(self.horizontalLayout_9)
        self.line_5 = QtWidgets.QFrame(DialogGraphics)
        self.line_5.setFrameShape(QtWidgets.QFrame.HLine)
        self.line_5.setFrameShadow(QtWidgets.QFrame.Sunken)
        self.line_5.setObjectName("line_5")
        self.verticalLayout.addWidget(self.line_5)
        self.btnClose = QtWidgets.QPushButton(DialogGraphics)
        font = QtGui.QFont()
        font.setFamily("PingFang")
        font.setBold(True)
        self.btnClose.setFont(font)
        self.btnClose.setObjectName("btnClose")
        self.verticalLayout.addWidget(self.btnClose)

        self.retranslateUi(DialogGraphics)
        self.comboInterp.setCurrentIndex(0)
        QtCore.QMetaObject.connectSlotsByName(DialogGraphics)

    def retranslateUi(self, DialogGraphics):
        _translate = QtCore.QCoreApplication.translate
        DialogGraphics.setWindowTitle(_translate("DialogGraphics", "图形设置"))
        self.label_46.setText(_translate("DialogGraphics", "UI 设置"))
        self.label_36.setText(_translate("DialogGraphics", "界面主题"))
        self.label_41.setText(_translate("DialogGraphics", "输入方式"))
        self.comboTheme.setItemText(0, _translate("DialogGraphics", "暗黑风格"))
        self.comboTheme.setItemText(1, _translate("DialogGraphics", "明亮风格"))
        self.comboInput.setItemText(0, _translate("DialogGraphics", "按位更改"))
        self.comboInput.setItemText(1, _translate("DialogGraphics", "自由更改"))
        self.label_35.setText(_translate("DialogGraphics", "图表设置"))
        self.label_39.setText(_translate("DialogGraphics", "最大刷新率"))
        self.spinMaxFps.setSuffix(_translate("DialogGraphics", "fps"))
        self.label_40.setText(_translate("DialogGraphics", "记录点数"))
        self.label_47.setText(_translate("DialogGraphics", "最小显示点数"))
        self.spinDataLength.setSuffix(_translate("DialogGraphics", "pts"))
        self.spinDisplayLength.setSuffix(_translate("DialogGraphics", "pts"))
        self.labelNumba.setText(_translate("DialogGraphics", "Numba 加速已启用"))
        self.checkBoxOpenGL.setText(_translate("DialogGraphics", "OpenGL加速"))
        self.checkBoxAntialias.setText(_translate("DialogGraphics", "曲线抗锯齿"))
        self.label_38.setText(_translate("DialogGraphics", "状态区设置"))
        self.label_43.setText(_translate("DialogGraphics", "最大刷新率"))
        self.label_44.setText(_translate("DialogGraphics", "插值方式"))
        self.spinStateFps.setSuffix(_translate("DialogGraphics", "fps"))
        self.comboInterp.setItemText(0, _translate("DialogGraphics", "不进行插值"))
        self.comboInterp.setItemText(1, _translate("DialogGraphics", "一位插值"))
        self.comboInterp.setItemText(2, _translate("DialogGraphics", "二位插值"))
        self.comboInterp.setItemText(3, _translate("DialogGraphics", "三位插值"))
        self.label_42.setText(_translate("DialogGraphics", "置零阈值"))
        self.spinStateVThres.setSuffix(_translate("DialogGraphics", "V"))
        self.spinStateIThres.setSuffix(_translate("DialogGraphics", "A"))
        self.labelCali_2.setText(_translate("DialogGraphics", "手动校准 (读/写)"))
        self.checkBoxUseCali.setText(_translate("DialogGraphics", "应用校准参数 (y=kx+b)"))
        self.spinCaliVk.setPrefix(_translate("DialogGraphics", "Vk="))
        self.spinCaliVb.setPrefix(_translate("DialogGraphics", "Vb="))
        self.spinCaliIk.setPrefix(_translate("DialogGraphics", "Ik="))
        self.spinCaliIb.setPrefix(_translate("DialogGraphics", "Ib="))
        self.spinCaliVk_2.setPrefix(_translate("DialogGraphics", "Vk="))
        self.spinCaliVb_2.setPrefix(_translate("DialogGraphics", "Vb="))
        self.spinCaliIk_2.setPrefix(_translate("DialogGraphics", "Ik="))
        self.spinCaliIb_2.setPrefix(_translate("DialogGraphics", "Ib="))
        self.btnClose.setText(_translate("DialogGraphics", "确定 / OK"))
