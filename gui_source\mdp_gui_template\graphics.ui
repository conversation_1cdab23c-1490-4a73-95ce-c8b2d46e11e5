<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>DialogGraphics</class>
 <widget class="QDialog" name="DialogGraphics">
  <property name="windowModality">
   <enum>Qt::WindowModal</enum>
  </property>
  <property name="enabled">
   <bool>true</bool>
  </property>
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>280</width>
    <height>640</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="minimumSize">
   <size>
    <width>280</width>
    <height>640</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>280</width>
    <height>640</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>图形设置</string>
  </property>
  <property name="sizeGripEnabled">
   <bool>false</bool>
  </property>
  <property name="modal">
   <bool>true</bool>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>2</number>
   </property>
   <item>
    <widget class="QLabel" name="label">
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>20</height>
      </size>
     </property>
     <property name="text">
      <string/>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QLabel" name="label_46">
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>28</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>999</width>
       <height>16777215</height>
      </size>
     </property>
     <property name="font">
      <font>
       <family>PingFang</family>
       <bold>true</bold>
      </font>
     </property>
     <property name="text">
      <string>UI 设置</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
     <property name="margin">
      <number>6</number>
     </property>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_7">
     <property name="spacing">
      <number>6</number>
     </property>
     <item>
      <widget class="QLabel" name="label_36">
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>24</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>999</width>
         <height>16777215</height>
        </size>
       </property>
       <property name="font">
        <font>
         <family>PingFang</family>
         <bold>true</bold>
        </font>
       </property>
       <property name="text">
        <string>界面主题</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignCenter</set>
       </property>
       <property name="margin">
        <number>6</number>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLabel" name="label_41">
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>24</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>999</width>
         <height>16777215</height>
        </size>
       </property>
       <property name="font">
        <font>
         <family>PingFang</family>
         <bold>true</bold>
        </font>
       </property>
       <property name="text">
        <string>输入方式</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignCenter</set>
       </property>
       <property name="margin">
        <number>6</number>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_6">
     <property name="spacing">
      <number>6</number>
     </property>
     <item>
      <widget class="QComboBox" name="comboTheme">
       <property name="font">
        <font>
         <family>PingFang</family>
         <bold>true</bold>
        </font>
       </property>
       <property name="layoutDirection">
        <enum>Qt::LeftToRight</enum>
       </property>
       <property name="editable">
        <bool>false</bool>
       </property>
       <property name="sizeAdjustPolicy">
        <enum>QComboBox::AdjustToContentsOnFirstShow</enum>
       </property>
       <item>
        <property name="text">
         <string>暗黑风格</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>明亮风格</string>
        </property>
       </item>
      </widget>
     </item>
     <item>
      <widget class="QComboBox" name="comboInput">
       <property name="font">
        <font>
         <family>PingFang</family>
         <bold>true</bold>
        </font>
       </property>
       <property name="layoutDirection">
        <enum>Qt::LeftToRight</enum>
       </property>
       <property name="editable">
        <bool>false</bool>
       </property>
       <property name="sizeAdjustPolicy">
        <enum>QComboBox::AdjustToContentsOnFirstShow</enum>
       </property>
       <item>
        <property name="text">
         <string>按位更改</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>自由更改</string>
        </property>
       </item>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="Line" name="line_3">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QLabel" name="label_35">
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>28</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>999</width>
       <height>16777215</height>
      </size>
     </property>
     <property name="font">
      <font>
       <family>PingFang</family>
       <bold>true</bold>
      </font>
     </property>
     <property name="text">
      <string>图表设置</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
     <property name="margin">
      <number>6</number>
     </property>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_13">
     <property name="spacing">
      <number>6</number>
     </property>
     <item>
      <widget class="QLabel" name="label_39">
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>24</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>999</width>
         <height>16777215</height>
        </size>
       </property>
       <property name="font">
        <font>
         <family>PingFang</family>
         <bold>true</bold>
        </font>
       </property>
       <property name="text">
        <string>最大刷新率</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignCenter</set>
       </property>
       <property name="margin">
        <number>6</number>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_12">
     <property name="spacing">
      <number>6</number>
     </property>
     <item>
      <widget class="QDoubleSpinBox" name="spinMaxFps">
       <property name="font">
        <font>
         <family>PingFang</family>
         <bold>true</bold>
        </font>
       </property>
       <property name="alignment">
        <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
       </property>
       <property name="suffix">
        <string>fps</string>
       </property>
       <property name="decimals">
        <number>1</number>
       </property>
       <property name="minimum">
        <double>1.000000000000000</double>
       </property>
       <property name="maximum">
        <double>120.000000000000000</double>
       </property>
       <property name="singleStep">
        <double>5.000000000000000</double>
       </property>
       <property name="value">
        <double>60.000000000000000</double>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_14">
     <property name="spacing">
      <number>6</number>
     </property>
     <item>
      <widget class="QLabel" name="label_40">
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>24</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>999</width>
         <height>16777215</height>
        </size>
       </property>
       <property name="font">
        <font>
         <family>PingFang</family>
         <bold>true</bold>
        </font>
       </property>
       <property name="text">
        <string>记录点数</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignCenter</set>
       </property>
       <property name="margin">
        <number>6</number>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLabel" name="label_47">
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>24</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>999</width>
         <height>16777215</height>
        </size>
       </property>
       <property name="font">
        <font>
         <family>PingFang</family>
         <bold>true</bold>
        </font>
       </property>
       <property name="text">
        <string>最小显示点数</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignCenter</set>
       </property>
       <property name="margin">
        <number>6</number>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_15">
     <property name="spacing">
      <number>6</number>
     </property>
     <item>
      <widget class="QSpinBox" name="spinDataLength">
       <property name="font">
        <font>
         <family>PingFang</family>
         <bold>true</bold>
        </font>
       </property>
       <property name="alignment">
        <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
       </property>
       <property name="suffix">
        <string>pts</string>
       </property>
       <property name="prefix">
        <string/>
       </property>
       <property name="minimum">
        <number>100</number>
       </property>
       <property name="maximum">
        <number>999999999</number>
       </property>
       <property name="singleStep">
        <number>100</number>
       </property>
       <property name="value">
        <number>500</number>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QSpinBox" name="spinDisplayLength">
       <property name="font">
        <font>
         <family>PingFang</family>
         <bold>true</bold>
        </font>
       </property>
       <property name="alignment">
        <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
       </property>
       <property name="suffix">
        <string>pts</string>
       </property>
       <property name="prefix">
        <string/>
       </property>
       <property name="minimum">
        <number>100</number>
       </property>
       <property name="maximum">
        <number>999999999</number>
       </property>
       <property name="singleStep">
        <number>100</number>
       </property>
       <property name="value">
        <number>500</number>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QLabel" name="labelNumba">
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>24</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>999</width>
       <height>16777215</height>
      </size>
     </property>
     <property name="font">
      <font>
       <family>PingFang</family>
       <bold>true</bold>
      </font>
     </property>
     <property name="text">
      <string>Numba 加速已启用</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
     <property name="margin">
      <number>6</number>
     </property>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_5">
     <property name="spacing">
      <number>6</number>
     </property>
     <item>
      <widget class="QCheckBox" name="checkBoxOpenGL">
       <property name="font">
        <font>
         <family>PingFang</family>
         <bold>true</bold>
        </font>
       </property>
       <property name="text">
        <string>OpenGL加速</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QCheckBox" name="checkBoxAntialias">
       <property name="font">
        <font>
         <family>PingFang</family>
         <bold>true</bold>
        </font>
       </property>
       <property name="text">
        <string>曲线抗锯齿</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="Line" name="line">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QLabel" name="label_38">
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>28</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>999</width>
       <height>16777215</height>
      </size>
     </property>
     <property name="font">
      <font>
       <family>PingFang</family>
       <bold>true</bold>
      </font>
     </property>
     <property name="text">
      <string>状态区设置</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
     <property name="margin">
      <number>6</number>
     </property>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_11">
     <property name="spacing">
      <number>6</number>
     </property>
     <item>
      <widget class="QLabel" name="label_43">
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>24</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>999</width>
         <height>16777215</height>
        </size>
       </property>
       <property name="font">
        <font>
         <family>PingFang</family>
         <bold>true</bold>
        </font>
       </property>
       <property name="text">
        <string>最大刷新率</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignCenter</set>
       </property>
       <property name="margin">
        <number>6</number>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLabel" name="label_44">
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>24</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>999</width>
         <height>16777215</height>
        </size>
       </property>
       <property name="font">
        <font>
         <family>PingFang</family>
         <bold>true</bold>
        </font>
       </property>
       <property name="text">
        <string>插值方式</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignCenter</set>
       </property>
       <property name="margin">
        <number>6</number>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_10">
     <property name="spacing">
      <number>6</number>
     </property>
     <item>
      <widget class="QDoubleSpinBox" name="spinStateFps">
       <property name="font">
        <font>
         <family>PingFang</family>
         <bold>true</bold>
        </font>
       </property>
       <property name="alignment">
        <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
       </property>
       <property name="suffix">
        <string>fps</string>
       </property>
       <property name="decimals">
        <number>1</number>
       </property>
       <property name="minimum">
        <double>1.000000000000000</double>
       </property>
       <property name="maximum">
        <double>120.000000000000000</double>
       </property>
       <property name="singleStep">
        <double>5.000000000000000</double>
       </property>
       <property name="value">
        <double>60.000000000000000</double>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QComboBox" name="comboInterp">
       <property name="font">
        <font>
         <family>PingFang</family>
         <bold>true</bold>
        </font>
       </property>
       <property name="layoutDirection">
        <enum>Qt::LeftToRight</enum>
       </property>
       <property name="editable">
        <bool>false</bool>
       </property>
       <property name="currentIndex">
        <number>0</number>
       </property>
       <property name="sizeAdjustPolicy">
        <enum>QComboBox::AdjustToContentsOnFirstShow</enum>
       </property>
       <item>
        <property name="text">
         <string>不进行插值</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>一位插值</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>二位插值</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>三位插值</string>
        </property>
       </item>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="Line" name="line_2">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QLabel" name="label_42">
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>28</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>999</width>
       <height>16777215</height>
      </size>
     </property>
     <property name="font">
      <font>
       <family>PingFang</family>
       <bold>true</bold>
      </font>
     </property>
     <property name="text">
      <string>置零阈值</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
     <property name="margin">
      <number>6</number>
     </property>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_2">
     <property name="spacing">
      <number>6</number>
     </property>
     <item>
      <widget class="QDoubleSpinBox" name="spinStateVThres">
       <property name="font">
        <font>
         <family>PingFang</family>
         <bold>true</bold>
        </font>
       </property>
       <property name="alignment">
        <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
       </property>
       <property name="suffix">
        <string>V</string>
       </property>
       <property name="decimals">
        <number>3</number>
       </property>
       <property name="minimum">
        <double>0.000000000000000</double>
       </property>
       <property name="maximum">
        <double>1.000000000000000</double>
       </property>
       <property name="singleStep">
        <double>0.001000000000000</double>
       </property>
       <property name="value">
        <double>0.000000000000000</double>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QDoubleSpinBox" name="spinStateIThres">
       <property name="font">
        <font>
         <family>PingFang</family>
         <bold>true</bold>
        </font>
       </property>
       <property name="alignment">
        <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
       </property>
       <property name="suffix">
        <string>A</string>
       </property>
       <property name="decimals">
        <number>3</number>
       </property>
       <property name="minimum">
        <double>0.000000000000000</double>
       </property>
       <property name="maximum">
        <double>1.000000000000000</double>
       </property>
       <property name="singleStep">
        <double>0.001000000000000</double>
       </property>
       <property name="value">
        <double>0.000000000000000</double>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="Line" name="line_4">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QLabel" name="labelCali_2">
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>24</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>999</width>
       <height>16777215</height>
      </size>
     </property>
     <property name="font">
      <font>
       <family>PingFang</family>
       <bold>true</bold>
      </font>
     </property>
     <property name="text">
      <string>手动校准 (读/写)</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
     <property name="margin">
      <number>6</number>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QCheckBox" name="checkBoxUseCali">
     <property name="font">
      <font>
       <family>PingFang</family>
       <bold>true</bold>
      </font>
     </property>
     <property name="text">
      <string>应用校准参数 (y=kx+b)</string>
     </property>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_3">
     <property name="spacing">
      <number>6</number>
     </property>
     <item>
      <widget class="QDoubleSpinBox" name="spinCaliVk">
       <property name="font">
        <font>
         <family>PingFang</family>
         <bold>true</bold>
        </font>
       </property>
       <property name="alignment">
        <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
       </property>
       <property name="prefix">
        <string>Vk=</string>
       </property>
       <property name="suffix">
        <string/>
       </property>
       <property name="decimals">
        <number>8</number>
       </property>
       <property name="minimum">
        <double>-100.000000000000000</double>
       </property>
       <property name="maximum">
        <double>100.000000000000000</double>
       </property>
       <property name="singleStep">
        <double>0.001000000000000</double>
       </property>
       <property name="value">
        <double>1.000000000000000</double>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QDoubleSpinBox" name="spinCaliVb">
       <property name="font">
        <font>
         <family>PingFang</family>
         <bold>true</bold>
        </font>
       </property>
       <property name="alignment">
        <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
       </property>
       <property name="prefix">
        <string>Vb=</string>
       </property>
       <property name="suffix">
        <string/>
       </property>
       <property name="decimals">
        <number>8</number>
       </property>
       <property name="minimum">
        <double>-100.000000000000000</double>
       </property>
       <property name="maximum">
        <double>100.000000000000000</double>
       </property>
       <property name="singleStep">
        <double>0.001000000000000</double>
       </property>
       <property name="value">
        <double>0.000000000000000</double>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_4">
     <property name="spacing">
      <number>6</number>
     </property>
     <item>
      <widget class="QDoubleSpinBox" name="spinCaliIk">
       <property name="font">
        <font>
         <family>PingFang</family>
         <bold>true</bold>
        </font>
       </property>
       <property name="alignment">
        <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
       </property>
       <property name="prefix">
        <string>Ik=</string>
       </property>
       <property name="suffix">
        <string/>
       </property>
       <property name="decimals">
        <number>8</number>
       </property>
       <property name="minimum">
        <double>-100.000000000000000</double>
       </property>
       <property name="maximum">
        <double>100.000000000000000</double>
       </property>
       <property name="singleStep">
        <double>0.001000000000000</double>
       </property>
       <property name="value">
        <double>1.000000000000000</double>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QDoubleSpinBox" name="spinCaliIb">
       <property name="font">
        <font>
         <family>PingFang</family>
         <bold>true</bold>
        </font>
       </property>
       <property name="alignment">
        <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
       </property>
       <property name="prefix">
        <string>Ib=</string>
       </property>
       <property name="suffix">
        <string/>
       </property>
       <property name="decimals">
        <number>8</number>
       </property>
       <property name="minimum">
        <double>-100.000000000000000</double>
       </property>
       <property name="maximum">
        <double>100.000000000000000</double>
       </property>
       <property name="singleStep">
        <double>0.001000000000000</double>
       </property>
       <property name="value">
        <double>0.000000000000000</double>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_8">
     <property name="spacing">
      <number>6</number>
     </property>
     <item>
      <widget class="QDoubleSpinBox" name="spinCaliVk_2">
       <property name="font">
        <font>
         <family>PingFang</family>
         <bold>true</bold>
        </font>
       </property>
       <property name="alignment">
        <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
       </property>
       <property name="prefix">
        <string>Vk=</string>
       </property>
       <property name="suffix">
        <string/>
       </property>
       <property name="decimals">
        <number>8</number>
       </property>
       <property name="minimum">
        <double>-100.000000000000000</double>
       </property>
       <property name="maximum">
        <double>100.000000000000000</double>
       </property>
       <property name="singleStep">
        <double>0.001000000000000</double>
       </property>
       <property name="value">
        <double>1.000000000000000</double>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QDoubleSpinBox" name="spinCaliVb_2">
       <property name="font">
        <font>
         <family>PingFang</family>
         <bold>true</bold>
        </font>
       </property>
       <property name="alignment">
        <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
       </property>
       <property name="prefix">
        <string>Vb=</string>
       </property>
       <property name="suffix">
        <string/>
       </property>
       <property name="decimals">
        <number>8</number>
       </property>
       <property name="minimum">
        <double>-100.000000000000000</double>
       </property>
       <property name="maximum">
        <double>100.000000000000000</double>
       </property>
       <property name="singleStep">
        <double>0.001000000000000</double>
       </property>
       <property name="value">
        <double>0.000000000000000</double>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_9">
     <property name="spacing">
      <number>6</number>
     </property>
     <item>
      <widget class="QDoubleSpinBox" name="spinCaliIk_2">
       <property name="font">
        <font>
         <family>PingFang</family>
         <bold>true</bold>
        </font>
       </property>
       <property name="alignment">
        <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
       </property>
       <property name="prefix">
        <string>Ik=</string>
       </property>
       <property name="suffix">
        <string/>
       </property>
       <property name="decimals">
        <number>8</number>
       </property>
       <property name="minimum">
        <double>-100.000000000000000</double>
       </property>
       <property name="maximum">
        <double>100.000000000000000</double>
       </property>
       <property name="singleStep">
        <double>0.001000000000000</double>
       </property>
       <property name="value">
        <double>1.000000000000000</double>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QDoubleSpinBox" name="spinCaliIb_2">
       <property name="font">
        <font>
         <family>PingFang</family>
         <bold>true</bold>
        </font>
       </property>
       <property name="alignment">
        <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
       </property>
       <property name="prefix">
        <string>Ib=</string>
       </property>
       <property name="suffix">
        <string/>
       </property>
       <property name="decimals">
        <number>8</number>
       </property>
       <property name="minimum">
        <double>-100.000000000000000</double>
       </property>
       <property name="maximum">
        <double>100.000000000000000</double>
       </property>
       <property name="singleStep">
        <double>0.001000000000000</double>
       </property>
       <property name="value">
        <double>0.000000000000000</double>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="Line" name="line_5">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QPushButton" name="btnClose">
     <property name="font">
      <font>
       <family>PingFang</family>
       <bold>true</bold>
      </font>
     </property>
     <property name="text">
      <string>确定 / OK</string>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
