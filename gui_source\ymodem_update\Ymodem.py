from time import sleep
import time

try:
    import crcmod
except ImportError:
    print("警告: crcmod库未安装，请运行: pip install crcmod")
    crcmod = None

from .BinFile import GetFileName, GetFileData, GetFileSize, GetFileDataAll, GetFileChunkCount

# YModem协议常量
SOH = 0x01  # 128字节数据包头
STX = 0x02  # 1024字节数据包头
EOT = 0x04  # 传输结束
ACK = 0x06  # 确认
NAK = 0x15  # 否认
CAN = 0x18  # 取消
C_CHAR = 0x43  # 'C'字符

def _crc16_xmodem(data):
    """CRC16-XMODEM计算"""
    if crcmod is not None:
        crc16 = crcmod.mkCrcFun(0x11021, rev=False, initCrc=0x0000, xorOut=0x0000)
        return crc16(data)
    else:
        # Fallback实现
        crc = 0x0000
        for byte in data:
            crc ^= (byte << 8)
            for _ in range(8):
                if crc & 0x8000:
                    crc = (crc << 1) ^ 0x1021
                else:
                    crc <<= 1
                crc &= 0xFFFF
        return crc

def _wait_for_char(serial, expected_char, timeout_seconds):
    """等待特定字符 - 连续阻塞读取版本，确保不错过任何字符"""
    original_timeout = serial.timeout
    try:
        start_time = time.time()
        serial.timeout = 0.1  # 100ms单次读取超时
        all_received_data = []
        
        print(f"[FORCE_DEBUG] 开始等待字符: 0x{expected_char:02X} ('{chr(expected_char) if 32 <= expected_char <= 126 else '非打印'}'), 总超时: {timeout_seconds}s")
        
        while time.time() - start_time < timeout_seconds:
            try:
                # 关键修复：连续阻塞读取，不给ACK任何逃脱机会
                byte_data = serial.read(1)  # 阻塞读取1字节，等待100ms
                
                if byte_data:  # 收到数据
                    all_received_data.append(byte_data)
                    byte_val = byte_data[0]
                    
                    print(f"[FORCE_DEBUG] 收到字节: 0x{byte_val:02X} ({'chr(' + str(byte_val) + ')' if 32 <= byte_val <= 126 else '非打印'})")
                    
                    # 立即检查是否为目标字符
                    if byte_val == expected_char:
                        print(f"[FORCE_DEBUG] ✓ 找到目标字符: 0x{expected_char:02X}!")
                        return True
                    
                    # 读取缓冲区中的其他数据（如果有）
                    while serial.in_waiting > 0:
                        extra_data = serial.read(serial.in_waiting)
                        all_received_data.append(extra_data)
                        print(f"[FORCE_DEBUG] 额外数据: {extra_data.hex()}")
                        
                        for extra_byte in extra_data:
                            if extra_byte == expected_char:
                                print(f"[FORCE_DEBUG] ✓ 在额外数据中找到目标字符: 0x{expected_char:02X}!")
                                return True
                
                # 无数据时不sleep，立即继续读取下一个字节
                
            except Exception as read_error:
                # 读取超时或异常，继续尝试
                continue
        
        # 超时后显示所有接收到的数据
        all_data = b''.join(all_received_data)
        print(f"[FORCE_DEBUG] ✗ 超时! 等待了{timeout_seconds}s，总接收数据: {all_data}")
        print(f"[FORCE_DEBUG] ✗ 总接收数据hex: {all_data.hex()}")
        print(f"[FORCE_DEBUG] ✗ 总接收数据字节: {[hex(b) for b in all_data]}")
        return False
        
    except Exception as e:
        print(f"[FORCE_DEBUG] 字符等待异常: {e}")
        return False
    finally:
        serial.timeout = original_timeout

def _send_initial_packet(serial, file_name, file_size):
    """发送YModem初始包 - 彻底重写版本"""
    try:
        print(f"[FORCE_DEBUG] === 开始构造初始包 ===")
        print(f"[FORCE_DEBUG] 文件名: '{file_name}', 文件大小: {file_size}")
        
        # 构造128字节数据部分
        data = bytearray(128)  # 全部初始化为0
        pos = 0
        
        # 文件名
        name_bytes = file_name.encode('ascii')
        print(f"[FORCE_DEBUG] 文件名字节: {name_bytes} (长度: {len(name_bytes)})")
        data[pos:pos+len(name_bytes)] = name_bytes
        pos += len(name_bytes)
        data[pos] = 0  # 文件名结束符
        pos += 1
        
        # 文件大小
        size_str = str(file_size)
        size_bytes = size_str.encode('ascii')
        print(f"[FORCE_DEBUG] 文件大小字节: {size_bytes} (长度: {len(size_bytes)})")
        data[pos:pos+len(size_bytes)] = size_bytes
        pos += len(size_bytes)
        data[pos] = 0  # 文件大小结束符
        pos += 1
        
        print(f"[FORCE_DEBUG] 数据部分前{pos}字节: {data[:pos].hex()}")
        print(f"[FORCE_DEBUG] 数据内容: {data[:pos].decode('ascii', errors='ignore')}")
        
        # 构造完整数据包
        packet = bytearray()
        packet.append(0x01)  # SOH包头
        packet.append(0x00)  # 包号0
        packet.append(0x00)  # 包号0（设备期望00而不是FF）
        packet.extend(data)   # 128字节数据
        
        # 计算CRC
        crc = _crc16_xmodem(data)
        packet.append((crc >> 8) & 0xFF)  # CRC高字节
        packet.append(crc & 0xFF)         # CRC低字节
        
        print(f"[FORCE_DEBUG] 完整包长度: {len(packet)}")
        print(f"[FORCE_DEBUG] 包头部分: {packet[:10].hex()}")
        print(f"[FORCE_DEBUG] 包尾部分: {packet[-10:].hex()}")
        print(f"[FORCE_DEBUG] CRC值: 0x{crc:04X}")
        
        # 确保只发送一次
        print(f"[FORCE_DEBUG] === 发送初始包 (一次性) ===")
        serial.write(bytes(packet))
        print(f"[FORCE_DEBUG] === 初始包发送完成 ===")
        
        return True
    except Exception as e:
        print(f"[FORCE_DEBUG] 发送初始包异常: {e}")
        return False

def _send_data_packet(serial, packet_number, data):
    """发送数据包"""
    try:
        # 数据填充到1024字节
        if len(data) < 1024:
            data = data + b'\x1A' * (1024 - len(data))
        else:
            data = data[:1024]
        
        # 构造数据包: STX + 包号 + 包号反码 + 数据(1024) + CRC(2)
        packet = bytearray()
        packet.append(STX)
        packet.append(packet_number & 0xFF)
        packet.append((~packet_number) & 0xFF)
        packet.extend(data)
        
        # 计算CRC
        crc = _crc16_xmodem(data)
        packet.append((crc >> 8) & 0xFF)
        packet.append(crc & 0xFF)
        
        serial.write(bytes(packet))
        return True
    except Exception as e:
        print(f"[ERROR] 发送数据包失败: {e}")
        return False

def YModemTransfer(firmware_data, file_name, serial, textview, progress_callback=None):
    """
    YModem传输 - 唯一的实现
    
    Args:
        firmware_data: bytes - 固件数据
        file_name: str - 文件名
        serial: 串口对象
        textview: 文本视图
        progress_callback: 进度回调
    """
    try:
        file_size = len(firmware_data)
        # textview.append("=== 新版YModemTransfer函数开始执行 ===")
        # textview.append(f"开始YModem传输: {file_name} ({file_size} 字节)")
        # print(f"[FORCE_DEBUG] YModemTransfer被调用: {file_name}, 大小: {file_size}")
        
        
        # 步骤2: 等待第一个'C'字符
        textview.append("等待设备请求...")
        if not _wait_for_char(serial, C_CHAR, 15.0):
            textview.append("等待设备请求超时")
            return False
        
        # 步骤3: 发送初始包
        textview.append("发送文件信息...")
        if not _send_initial_packet(serial, file_name, file_size):
            textview.append("发送文件信息失败")
            return False
        
        # 步骤4: 等待设备发送数据传输'C'字符
        textview.append("等待设备发送数据传输握手 ...")
        if not _wait_for_char(serial, C_CHAR, 30.0):
            textview.append("等待数据传输握手超时")
            return False
        
        textview.append("✓ 检测到数据传输握手，准备发送数据包")
        
        # 步骤6: 数据传输
        textview.append("开始数据传输...")
        packet_number = 1
        sent_bytes = 0
        
        while sent_bytes < file_size:
            # Sleep 1ms
            sleep(0.002)
            # 准备数据块
            remaining = file_size - sent_bytes
            chunk_size = min(1024, remaining)
            chunk = firmware_data[sent_bytes:sent_bytes + chunk_size]
            
            # 发送数据包
            if not _send_data_packet(serial, packet_number, chunk):
                textview.append(f"发送数据包{packet_number}失败")
                return False
            
            # 等待ACK
            if not _wait_for_char(serial, ACK, 5.0):
                textview.append(f"数据包{packet_number}ACK确认超时")
                if not _wait_for_char(serial, C_CHAR, 10.0):
                    textview.append(f"数据包{packet_number}确认超时")
                    return False



            # 更新进度
            sent_bytes += chunk_size
            packet_number += 1
            
            # 进度条优化：增加每个包传输的进度更新频率，避免跳跃
            if progress_callback:
                progress = min(99, int((sent_bytes / file_size) * 100))
                # 检查是否有实际进度变化 - 减少阈值以增加更新频率
                if not hasattr(progress_callback, '_last_progress'):
                    progress_callback._last_progress = -1
                # 每个包都更新进度，确保平滑的进度显示
                if progress > progress_callback._last_progress:  # 任何进度增长都更新
                    progress_callback._last_progress = progress
                    progress_callback(progress)
            
            if packet_number % 50 == 0:
                textview.append(f"已发送 {packet_number-1} 个包 ({sent_bytes}/{file_size} 字节)")
        
        # 步骤7: 发送结束信号
        textview.append("发送结束信号...")
        serial.write(bytes([EOT]))
        
        # 等待设备确认EOT
        # textview.append("等待EOT确认...")
        if not _wait_for_char(serial, NAK, 2.0):
            textview.append("EOT确认超时，但继续发送空包")
        else:
            textview.append("EOT确认成功")
        
        # 等待设备请求空包（应该发送'C'）
        # textview.append("等待空包请求...")
        if _wait_for_char(serial, C_CHAR, 2.0):
            textview.append("收到空包请求，发送空包")
        
        # 发送空包
        empty_packet = bytearray()
        empty_packet.append(SOH)
        empty_packet.append(0x00)
        empty_packet.append(0x00)
        empty_packet.extend([0x00] * 128)
        crc = _crc16_xmodem(bytes([0x00] * 128))
        empty_packet.append((crc >> 8) & 0xFF)
        empty_packet.append(crc & 0xFF)
        serial.write(bytes(empty_packet))
        
        # 等待空包确认
        textview.append("等待空包确认...")
        # if _wait_for_char(serial, ACK, 10.0):
        #     textview.append("空包确认成功，传输完成")
        # else:
        #     textview.append("空包确认超时，但传输可能已完成")
        # else:
        #     textview.append("未收到空包请求，跳过空包发送")
        
        # 完成
        if progress_callback:
            progress_callback(100)
        
        textview.append("固件传输完成！")
        return True
        
    except Exception as e:
        textview.append(f"传输错误: {e}")
        return False

# 兼容性别名
YmodemFromMemoryStable = YModemTransfer
YmodemFromMemory = YModemTransfer

def Ymodem(file_path, serial, textview, progress_callback=None):
    """兼容老接口的文件版本"""
    try:
        firmware_data = GetFileDataAll(file_path)
        if firmware_data is False:
            return False
        
        file_name_parts = GetFileName(file_path)
        if file_name_parts[0] is False or file_name_parts[1] is False:
            return False
        
        file_name = file_name_parts[0] + file_name_parts[1]
        return YModemTransfer(firmware_data, file_name, serial, textview, progress_callback)
    except Exception as e:
        print(f"Ymodem兼容函数错误: {e}")
        return False