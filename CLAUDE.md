# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

WPX-QT-GUI is a PyQt5-based desktop application for controlling WPX (Wireless Power) devices via serial communication. The project consists of two main components:

1. **Controller Module** (`controller/`): Low-level device communication using WPX protocol over serial
2. **GUI Application** (`gui_source/`): PyQt5-based user interface with real-time monitoring and firmware update capabilities

## Key Architecture Components

### Device Communication Stack
- `controller/pd_pocket.py`: Main device interface class `PDPocket` for device discovery and control
- `controller/wpx_protocol.py`: Protocol implementation with frame structure, commands, and data parsing
- `controller/serial_reader.py`: Threaded serial communication handler
- Device discovery targets specific VID/PID: 0x0906:0x5A3E (normal mode), 0x07E7:0x5A42 (bootloader mode)

### GUI Framework
- `gui_source/mdp_gui.py`: Main application window with PyQtGraph plotting and device controls
- `gui_source/mdp_main.py`: Application entry point with PyInstaller splash screen support
- `gui_source/mdp_gui_template/`: UI components generated from Qt Designer files (.ui → .py)
- `gui_source/qframelesswindow/`: Cross-platform frameless window implementation

### Firmware Update System
- `gui_source/ymodem_update/`: YModem protocol implementation for firmware flashing
- `gui_source/auto_firmware_flasher.py`: Automated firmware update workflow
- `gui_source/firmware_downloader.py`: Remote firmware download capability

## Development Commands

### Building the Application
```bash
# Windows - Build both standard and numba-optimized versions (parallel builds)
cd gui_source
powershell .\build-x64.ps1

# Linux
pyinstaller mdp_linux.spec

# Individual builds
pyinstaller mdp.spec                    # Standard build
pyinstaller mdp_numba.spec             # Numba-optimized build

# Build with UPX compression (Windows)
pyinstaller --noconfirm --upx-dir C:\Toolchains\upx .\mdp.spec
```

### Development Setup
```bash
# Install dependencies
cd gui_source
pip install -r requirements.txt        # Windows
pip install -r requirements_linux.txt  # Linux  
pip install -r requirements_macos.txt  # macOS

# Run development version
python mdp_main.py

# Enable debug logging
python mdp_main.py --debug
# OR
export MDP_ENABLE_LOG=1
python mdp_main.py
```

### Testing and Validation
```bash
# Test connection without GUI
python test_connection_fix.py

# Test firmware update functionality
python test_full_update.py

# Test speed optimization features
python test_speed_optimization.py

# Find connected devices
python -c "from controller import find_all_devices; print(find_all_devices())"
```

## Code Conventions

### Python Style
- Use `loguru` for all logging (already configured in mdp_gui.py)
- Import controller modules: `from controller import PDPocket, find_all_devices`
- PyQt5 components: `from PyQt5 import QtCore, QtGui, QtWidgets`
- Real-time plotting: `import pyqtgraph as pg`

### Serial Communication Pattern
```python
# Device discovery and connection
devices = find_all_devices()
pd_device = PDPocket(port_name)
pd_device.connect()

# Protocol communication via wpx_protocol
# Frame structure: SOF(0xEE) + Length + Seq + CRC8 + CMD + Data + CRC16
```

### GUI Threading
- Use QtCore.QThread for background operations
- Serial communication runs in separate thread via SerialReader
- UI updates must use Qt signals/slots for thread safety

## Build Configurations

### PyInstaller Specs
- `mdp.spec`: Standard build with UPX compression
- `mdp_numba.spec`: Includes Numba acceleration for performance
- `mdp_linux.spec`: Linux-specific build configuration
- All specs exclude heavy dependencies (llvmlite, matplotlib, scipy, etc.) to reduce size

### Assets Included in Build
- `icon.ico`: Application icon
- `en_US.qm`: Translation file
- `PingFang Bold.ttf`: Unified PingFang font
- `Li-ion.csv`: Battery curve data
- `booting.png`: Splash screen image

## Important File Locations

- Application entry: `gui_source/mdp_main.py`
- Main GUI logic: `gui_source/mdp_gui.py`
- Device interface: `controller/pd_pocket.py`
- Protocol definitions: `controller/wpx_protocol.py`
- Build scripts: `gui_source/build-x64.ps1` (Windows)
- Dependencies: `gui_source/requirements*.txt`

## Development Notes

- The application supports device auto-discovery via USB VID/PID matching
- Firmware updates use YModem protocol with automatic bootloader mode switching
- Real-time data visualization uses PyQtGraph for performance
- Cross-platform window effects implemented in `qframelesswindow/`
- Logging automatically saves to `pd_pocket.log` when debug mode enabled
- Speed optimizations implemented in YModem protocol (reduced initialization delays)
- No formal linting or test framework configured - validation done via manual test scripts