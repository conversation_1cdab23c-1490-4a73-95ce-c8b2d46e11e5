<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE TS>
<TS version="2.1" language="en" sourcelanguage="aho">
<context>
    <name>MainWindow</name>
    <message>
        <location filename="mainwindow_ui.py" line="1646"/>
        <source>WPX-01 无线充电测试仪上位机</source>
        <translation>WPX-01 Wireless Charging Tester GUI</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1647"/>
        <source>  电压</source>
        <translation>Voltage</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1648"/>
        <source>  电流</source>
        <translation>Current</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1649"/>
        <source>  功率</source>
        <translation>Power</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1650"/>
        <source>  容量</source>
        <translation>Capacity</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1651"/>
        <source>平均功率</source>
        <translation>AVG</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1652"/>
        <source>能量</source>
        <translation>Energy</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1653"/>
        <source>X/Y数据:</source>
        <translation>X/YData</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1654"/>
        <source>上窗口数据</source>
        <translation>Upper Window Data</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1665"/>
        <source>电压</source>
        <translation>Voltage</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1666"/>
        <source>电流</source>
        <translation>Current</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1667"/>
        <source>功率</source>
        <translation>Power</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1668"/>
        <source>核心温度</source>
        <translation>Core Temp</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1669"/>
        <source>负载温度</source>
        <translation>Load Temp</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1670"/>
        <source>外部探头温度</source>
        <translation>External Probe Temp</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1671"/>
        <source>整流桥温度</source>
        <translation>Rectifier Bridge Temp</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1672"/>
        <source>线圈温度</source>
        <translation>Coil Temp</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1673"/>
        <source>无</source>
        <translation>None</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1664"/>
        <source>下窗口数据</source>
        <translation>Lower Window Data</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1674"/>
        <source>采样</source>
        <translation>Sampling</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1675"/>
        <source>ms</source>
        <translation>ms</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1676"/>
        <source>实时采样率</source>
        <translation>Real-Time Sampling Rate</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1677"/>
        <source>0.0Hz</source>
        <translation></translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1678"/>
        <source>将原始数据记录到CSV文件，不受图形缓冲区限制</source>
        <translation>Log Raw Data to CSV</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1679"/>
        <source>录制</source>
        <translation>Record</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1680"/>
        <source>导出当前数据缓冲区的有效数据</source>
        <translation></translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1681"/>
        <source>导出</source>
        <translation>Export Current Data Buffer</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1682"/>
        <source>切换数据波形是否自动适应窗口</source>
        <translation>Toggle Auto-Scaling of Data Waveform</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1683"/>
        <source>适应</source>
        <translation>Auto-Scale</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1684"/>
        <source>切换数据监控悬浮窗</source>
        <translation>Toggle Data Monitor Floating Window</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1685"/>
        <source>浮窗</source>
        <translation>Floating Window</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1686"/>
        <source>清零平均功率和能量累计</source>
        <translation>Reset Average Power and Energy Accumulation</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1687"/>
        <source>回零</source>
        <translation>Reset</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1688"/>
        <source>清空波形数据缓冲区</source>
        <translation>Clear Waveform Data Buffer</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1689"/>
        <source>清空</source>
        <translation>Clear</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1690"/>
        <source>停止数据波形刷新（数据缓冲区仍在更新）</source>
        <translation>Pause Waveform Refresh</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1691"/>
        <source>暂停</source>
        <translation>Pause</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1692"/>
        <source>00:00:00</source>
        <translation></translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1712"/>
        <source>0W</source>
        <translation></translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1694"/>
        <source>线圈温度:</source>
        <translation>Coil Temp:</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1695"/>
        <source>0%</source>
        <translation></translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1696"/>
        <source>握手功率:</source>
        <translation>Nego Power:</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1697"/>
        <source>核心温度:</source>
        <translation>Core Temp:</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1698"/>
        <source>RP:</source>
        <translation></translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1699"/>
        <source>信号强度:</source>
        <translation>Signal Strength:</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1700"/>
        <source>负载温度:</source>
        <translation>ELoad Temp:</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1715"/>
        <source>0℃</source>
        <translation></translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1702"/>
        <source>外部探头温度:</source>
        <translation>External Probe Temp:</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1799"/>
        <source>0</source>
        <translation></translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1707"/>
        <source>整流桥温度:</source>
        <translation>Rectifier Bridge Temp:</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1708"/>
        <source>CE:</source>
        <translation></translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1709"/>
        <source>0 Khz</source>
        <translation></translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1710"/>
        <source>-</source>
        <translation></translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1713"/>
        <source>工作频率:</source>
        <translation>Working freq:</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1714"/>
        <source>潜在功率:</source>
        <translation>Potential Power:</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1716"/>
        <source>握手协议:</source>
        <translation>Protocol:</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1717"/>
        <source>测试时间:</source>
        <translation>Test Time:</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1718"/>
        <source>🔋 协议配置</source>
        <translation>Protocol Configuration</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1719"/>
        <source>运行协议：</source>
        <translation>Active Protocol: </translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1720"/>
        <source>BPP 5W</source>
        <translation></translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1721"/>
        <source>EPP 15W</source>
        <translation></translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1722"/>
        <source>Qi2 MPP 15W</source>
        <translation></translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1723"/>
        <source>Qi2.2 MPP 25W</source>
        <translation></translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1724"/>
        <source>Apple 7.5W</source>
        <translation></translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1725"/>
        <source>Apple MagSafe 15W</source>
        <translation></translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1726"/>
        <source>FAKE MagSafe</source>
        <translation></translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1727"/>
        <source>PPDE 10W</source>
        <translation></translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1728"/>
        <source>Sumsung 15W</source>
        <translation></translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1729"/>
        <source>Xiaomi </source>
        <translation></translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1730"/>
        <source>Huawei</source>
        <translation></translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1731"/>
        <source>OPPO</source>
        <translation></translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1732"/>
        <source>VIVO</source>
        <translation></translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1733"/>
        <source>Redmi</source>
        <translation></translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1734"/>
        <source>Realme</source>
        <translation></translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1735"/>
        <source>iQOO</source>
        <translation></translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1736"/>
        <source>Meizu</source>
        <translation></translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1737"/>
        <source>Honor</source>
        <translation></translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1738"/>
        <source>Generic</source>
        <translation></translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1858"/>
        <source>设置</source>
        <translation>Setting</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1740"/>
        <source>FOD补偿：</source>
        <translation>FOD Compensation:</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1741"/>
        <source>+500</source>
        <translation></translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1743"/>
        <source>负载电压电流设置:(非必要请勿修改)</source>
        <translation>Load Voltage/Current Settings: (_</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1744"/>
        <source>6.0</source>
        <translation></translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1745"/>
        <source>V</source>
        <translation></translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1746"/>
        <source>1.0</source>
        <translation></translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1747"/>
        <source>A</source>
        <translation></translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1749"/>
        <source>发送电池充满指令</source>
        <translation>Send Battery Full Command</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1750"/>
        <source>⚡ 协议控制</source>
        <translation> Protocol Control</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1751"/>
        <source>Control Error:</source>
        <translation></translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1752"/>
        <source>发送所设置的CE大小</source>
        <translation>Send Configured CE Value</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1771"/>
        <source>发送</source>
        <translation>Send</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1754"/>
        <source>EPT:</source>
        <translation></translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1755"/>
        <source>充满 / 01</source>
        <translation>Fully Charged / 01</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1756"/>
        <source>故障 / 02</source>
        <translation>General Error / 02</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1757"/>
        <source>过温 / 03</source>
        <translation>Over Temperature / 03</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1758"/>
        <source>过压 / 04</source>
        <translation>Over Voltage / 04</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1759"/>
        <source>过流 / 05</source>
        <translation>Over Current / 05</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1760"/>
        <source>电池故障 / 06</source>
        <translation>Battery Failure / 06</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1761"/>
        <source>工作点异常 / 08</source>
        <translation>Operating Point Abnormal / 08</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1762"/>
        <source>协商异常 / 0A</source>
        <translation>Negotiation Error / 0A</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1763"/>
        <source>重启 / 0B</source>
        <translation>Restart / 0B</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1764"/>
        <source>Reping / 0C</source>
        <translation></translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1765"/>
        <source>NFC / 0D</source>
        <translation></translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1766"/>
        <source>NA / 0E</source>
        <translation></translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1767"/>
        <source>NA / 0F</source>
        <translation></translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1769"/>
        <source>指定数据包：（16进制，空格分隔）</source>
        <translation>Specified Packet: (Hexadecimal...)</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1770"/>
        <source>18 01 19</source>
        <translation></translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1772"/>
        <source>启动自动化协议检测</source>
        <translation>Start Automated Protocol Detection</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1773"/>
        <source>调试</source>
        <translation>Debug</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1774"/>
        <source>调试设置</source>
        <translation>Debug Settings</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1775"/>
        <source>EPP 数据</source>
        <translation>Extended Power Profile</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1776"/>
        <source>NRS 比例:</source>
        <translation>NRS Ratio:</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1777"/>
        <source>双工通信:</source>
        <translation>Duplex Communication:</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1778"/>
        <source>WPID:</source>
        <translation></translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1782"/>
        <source>制造商 ID:</source>
        <translation>Manufacturer ID:</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1783"/>
        <source>支持鉴权:</source>
        <translation>Authentication Supported:</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1784"/>
        <source>带外通信:</source>
        <translation>Out-of-Band Communication:</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1788"/>
        <source>MPP 数据</source>
        <translation>Magnetic Power Profile</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1792"/>
        <source>设备ID:</source>
        <translation>Device ID:</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1794"/>
        <source>并发数据流:</source>
        <translation>Concurrent Data Streams:</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1797"/>
        <source>APP:</source>
        <translation></translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1800"/>
        <source>支持校准:</source>
        <translation>Calibration Supported:</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1801"/>
        <source>功率限制原因:</source>
        <translation>Power Limitation Reason:</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1802"/>
        <source>g_goil_rx:</source>
        <translation></translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1803"/>
        <source>UID:</source>
        <translation></translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1804"/>
        <source>缓存大小:</source>
        <translation>Buffer Size:</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1805"/>
        <source>协议</source>
        <translation>Protocol</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1806"/>
        <source>协议信息查看</source>
        <translation>View Protocol Information</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1807"/>
        <source>点击开启功能</source>
        <translation>Click to Enable Function</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1808"/>
        <source>Charge Time: 00:00:00</source>
        <translation></translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1809"/>
        <source>🔋 充电配置</source>
        <translation>Charging Configuration</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1810"/>
        <source>从文件加载充电曲线数据</source>
        <translation>Load Charging Curve from File</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1811"/>
        <source>加载曲线</source>
        <translation>Load Curve</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1812"/>
        <source>预览当前充电曲线</source>
        <translation>Preview Current Charging Curve</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1813"/>
        <source>查看曲线</source>
        <translation>View Curve</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1814"/>
        <source>充电曲线</source>
        <translation>Charging Curve</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1815"/>
        <source>选择要使用的充电曲线类型</source>
        <translation>Select Charging Curve to Use</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1816"/>
        <source>⚡ 充电控制</source>
        <translation>Charging Control</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1817"/>
        <source>目标电量</source>
        <translation>Target Battery Level</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1818"/>
        <source>当前电量</source>
        <translation>Current Battery Level</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1819"/>
        <source>设置电池当前电量百分比</source>
        <translation>Set Current Battery Percentage</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1822"/>
        <source>%</source>
        <translation></translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1821"/>
        <source>设置充电目标电量百分比</source>
        <translation>Set Target Battery Percentage</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1823"/>
        <source>执行频率</source>
        <translation>Execution Frequency</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1824"/>
        <source>设置充电模拟计算频率</source>
        <translation>Set Charging Simulation Calculation Frequency</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1825"/>
        <source>Hz</source>
        <translation></translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1826"/>
        <source>🔧 电池参数</source>
        <translation>Battery Parameters</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1827"/>
        <source>单节容量</source>
        <translation>Single Cell Capacity</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1828"/>
        <source>设置单节电池的能量容量</source>
        <translation>Set Energy Capacity of Single Cell</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1829"/>
        <source>Wh</source>
        <translation></translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1830"/>
        <source>单节内阻</source>
        <translation>Single Cell Internal Resistance</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1831"/>
        <source>设置单节电池的内阻值</source>
        <translation>Set Internal Resistance of Single Cell</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1832"/>
        <source>mΩ</source>
        <translation></translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1833"/>
        <source>串联节数</source>
        <translation>Number of Series Cells</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1834"/>
        <source>设置电池串联的节数</source>
        <translation>Set Number of Cells in Series</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1835"/>
        <source>S</source>
        <translation></translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1836"/>
        <source>充电模拟</source>
        <translation>Charging Sim</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1837"/>
        <source>电池充电模拟测试</source>
        <translation>Battery Charging Simulation Test</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1838"/>
        <source>负载配置</source>
        <translation>Load Configuration</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1839"/>
        <source>外部负载</source>
        <translation>External Load</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1840"/>
        <source>内部负载</source>
        <translation>Internal Load</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1841"/>
        <source>设备信息</source>
        <translation>Device Information</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1842"/>
        <source>固件版本：</source>
        <translation>Firmware Version:</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1847"/>
        <source>0.0.0</source>
        <translation></translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1844"/>
        <source>内核版本：</source>
        <translation>Kernel Version:</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1846"/>
        <source>硬件版本：</source>
        <translation>Hardware Version:</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1848"/>
        <source>查询版本</source>
        <translation>Query Version</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1849"/>
        <source>查询 SN</source>
        <translation>Query SN</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1862"/>
        <source>系统设置</source>
        <translation>System Settings</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1851"/>
        <source>电压补偿：</source>
        <translation>Voltage Compensation:</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1853"/>
        <source>电流补偿：</source>
        <translation>Current Compensation:</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1855"/>
        <source>频率补偿：</source>
        <translation>Frequency Compensation:</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1857"/>
        <source>恢复出厂设置</source>
        <translation>Restore Factory Settings</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1859"/>
        <source>Qi协议分析仪</source>
        <translation>Qi Protocol Analyzer</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1860"/>
        <source>软件/固件更新</source>
        <translation>Software/Firmware Update</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1861"/>
        <source>图形设置</source>
        <translation>Graph Settings</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1863"/>
        <source>未连接</source>
        <translation>Not Connected</translation>
    </message>
    <message>
        <location filename="mainwindow_ui.py" line="1864"/>
        <source>连接/断开</source>
        <translation>Connect / Disconnect</translation>
    </message>
</context>
</TS>
