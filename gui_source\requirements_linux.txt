# This file was autogenerated by uv via the following command:
#    uv pip compile -
altgraph==0.17.4
    # via pyinstaller
cffi==1.17.1
    # via xcffib
colorama==0.4.6
contourpy==1.3.0
    # via matplotlib
cycler==0.12.1
    # via matplotlib
darkdetect==0.7.1
    # via pyqtdarktheme
fonttools==4.54.1
    # via matplotlib
kiwisolver==1.4.7
    # via matplotlib
llvmlite==0.43.0
    # via numba
loguru==0.7.2
markdown-it-py==3.0.0
    # via rich
matplotlib==3.9.2
mdurl==0.1.2
    # via markdown-it-py
numba==0.60.0
numpy==1.24.4
    # via
    #   contourpy
    #   matplotlib
    #   numba
    #   pyqtgraph
packaging==24.1
    # via
    #   matplotlib
    #   pyinstaller
    #   pyinstaller-hooks-contrib
    #   qtpy
pefile==2023.2.7
pillow==11.0.0
    # via matplotlib
pycparser==2.22
    # via cffi
pygments==2.18.0
    # via
    #   rich
    #   superqt
pyinstaller==6.11.0
pyinstaller-hooks-contrib==2024.9
    # via pyinstaller
pyparsing==3.2.0
    # via matplotlib
pyqt5==5.15.6
pyqt5-qt5==5.15.2
    # via pyqt5
pyqt5-sip==12.10.1
    # via pyqt5
pyqtdarktheme==2.1.0
pyqtgraph==0.13.7
pyserial==3.5
python-dateutil==2.9.0.post0
    # via matplotlib
qtpy==2.4.2
    # via superqt
rich==13.9.4
setuptools==75.3.0
    # via
    #   pyinstaller
    #   pyinstaller-hooks-contrib
simple-pid==2.0.1
six==1.16.0
    # via python-dateutil
superqt==0.6.7
typing-extensions==4.12.2
    # via superqt
xcffib==1.5.0
