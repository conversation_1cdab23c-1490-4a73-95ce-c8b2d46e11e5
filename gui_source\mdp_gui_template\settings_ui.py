# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'mdp_gui_template\settings.ui'
#
# Created by: PyQt5 UI code generator 5.15.11
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_DialogSettings(object):
    def setupUi(self, DialogSettings):
        DialogSettings.setObjectName("DialogSettings")
        DialogSettings.setWindowModality(QtCore.Qt.WindowModal)
        DialogSettings.setEnabled(True)
        DialogSettings.resize(303, 460)
        DialogSettings.setMinimumSize(QtCore.QSize(303, 460))
        DialogSettings.setMaximumSize(QtCore.QSize(303, 460))
        DialogSettings.setSizeGripEnabled(False)
        DialogSettings.setModal(True)
        self.verticalLayout = QtWidgets.QVBoxLayout(DialogSettings)
        self.verticalLayout.setObjectName("verticalLayout")
        self.label = QtWidgets.QLabel(DialogSettings)
        self.label.setMinimumSize(QtCore.QSize(0, 20))
        self.label.setMaximumSize(QtCore.QSize(16777215, 20))
        self.label.setText("")
        self.label.setObjectName("label")
        self.verticalLayout.addWidget(self.label)
        self.label_36 = QtWidgets.QLabel(DialogSettings)
        self.label_36.setMinimumSize(QtCore.QSize(0, 28))
        font = QtGui.QFont()
        font.setFamily("PingFang")
        self.label_36.setFont(font)
        self.label_36.setAlignment(QtCore.Qt.AlignCenter)
        self.label_36.setObjectName("label_36")
        self.verticalLayout.addWidget(self.label_36)
        self.horizontalLayout_33 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_33.setObjectName("horizontalLayout_33")
        self.label_34 = QtWidgets.QLabel(DialogSettings)
        self.label_34.setMinimumSize(QtCore.QSize(0, 28))
        font = QtGui.QFont()
        font.setFamily("PingFang")
        self.label_34.setFont(font)
        self.label_34.setAlignment(QtCore.Qt.AlignCenter)
        self.label_34.setObjectName("label_34")
        self.horizontalLayout_33.addWidget(self.label_34)
        self.comboBoxPort = QtWidgets.QComboBox(DialogSettings)
        font = QtGui.QFont()
        font.setFamily("PingFang")
        self.comboBoxPort.setFont(font)
        self.comboBoxPort.setObjectName("comboBoxPort")
        self.comboBoxPort.addItem("")
        self.horizontalLayout_33.addWidget(self.comboBoxPort)
        self.horizontalLayout_33.setStretch(0, 1)
        self.horizontalLayout_33.setStretch(1, 1)
        self.verticalLayout.addLayout(self.horizontalLayout_33)
        self.line = QtWidgets.QFrame(DialogSettings)
        self.line.setFrameShadow(QtWidgets.QFrame.Plain)
        self.line.setFrameShape(QtWidgets.QFrame.HLine)
        self.line.setObjectName("line")
        self.verticalLayout.addWidget(self.line)
        self.label_44 = QtWidgets.QLabel(DialogSettings)
        self.label_44.setMinimumSize(QtCore.QSize(0, 28))
        font = QtGui.QFont()
        font.setFamily("PingFang")
        self.label_44.setFont(font)
        self.label_44.setAlignment(QtCore.Qt.AlignCenter)
        self.label_44.setObjectName("label_44")
        self.verticalLayout.addWidget(self.label_44)
        self.verticalLayout_3 = QtWidgets.QVBoxLayout()
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.Chinese_rb = QtWidgets.QRadioButton(DialogSettings)
        self.Chinese_rb.setChecked(True)
        self.Chinese_rb.setObjectName("Chinese_rb")
        self.verticalLayout_3.addWidget(self.Chinese_rb)
        self.English_rb = QtWidgets.QRadioButton(DialogSettings)
        self.English_rb.setObjectName("English_rb")
        self.verticalLayout_3.addWidget(self.English_rb)
        self.verticalLayout.addLayout(self.verticalLayout_3)
        self.horizontalLayout_35 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_35.setObjectName("horizontalLayout_35")
        self.btnSetMaxP = QtWidgets.QPushButton(DialogSettings)
        font = QtGui.QFont()
        font.setFamily("PingFang")
        self.btnSetMaxP.setFont(font)
        self.btnSetMaxP.setObjectName("btnSetMaxP")
        self.horizontalLayout_35.addWidget(self.btnSetMaxP)
        self.spinBoxMaxP = QtWidgets.QDoubleSpinBox(DialogSettings)
        font = QtGui.QFont()
        font.setFamily("PingFang")
        self.spinBoxMaxP.setFont(font)
        self.spinBoxMaxP.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.spinBoxMaxP.setDecimals(0)
        self.spinBoxMaxP.setMinimum(1.0)
        self.spinBoxMaxP.setMaximum(25.0)
        self.spinBoxMaxP.setSingleStep(1.0)
        self.spinBoxMaxP.setStepType(QtWidgets.QAbstractSpinBox.AdaptiveDecimalStepType)
        self.spinBoxMaxP.setProperty("value", 25.0)
        self.spinBoxMaxP.setObjectName("spinBoxMaxP")
        self.horizontalLayout_35.addWidget(self.spinBoxMaxP)
        self.horizontalLayout_35.setStretch(0, 1)
        self.horizontalLayout_35.setStretch(1, 1)
        self.verticalLayout.addLayout(self.horizontalLayout_35)
        self.horizontalLayout_38 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_38.setObjectName("horizontalLayout_38")
        self.btnCali1 = QtWidgets.QPushButton(DialogSettings)
        font = QtGui.QFont()
        font.setFamily("PingFang")
        self.btnCali1.setFont(font)
        self.btnCali1.setObjectName("btnCali1")
        self.horizontalLayout_38.addWidget(self.btnCali1)
        self.btnCali2 = QtWidgets.QPushButton(DialogSettings)
        font = QtGui.QFont()
        font.setFamily("PingFang")
        self.btnCali2.setFont(font)
        self.btnCali2.setObjectName("btnCali2")
        self.horizontalLayout_38.addWidget(self.btnCali2)
        self.horizontalLayout_38.setStretch(0, 1)
        self.horizontalLayout_38.setStretch(1, 1)
        self.verticalLayout.addLayout(self.horizontalLayout_38)
        self.horizontalLayout_4 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_4.setObjectName("horizontalLayout_4")
        self.btnEnableShortProtect = QtWidgets.QPushButton(DialogSettings)
        font = QtGui.QFont()
        font.setFamily("PingFang")
        self.btnEnableShortProtect.setFont(font)
        self.btnEnableShortProtect.setObjectName("btnEnableShortProtect")
        self.horizontalLayout_4.addWidget(self.btnEnableShortProtect)
        self.btnDisableShortProtect = QtWidgets.QPushButton(DialogSettings)
        font = QtGui.QFont()
        font.setFamily("PingFang")
        self.btnDisableShortProtect.setFont(font)
        self.btnDisableShortProtect.setObjectName("btnDisableShortProtect")
        self.horizontalLayout_4.addWidget(self.btnDisableShortProtect)
        self.horizontalLayout_4.setStretch(0, 1)
        self.horizontalLayout_4.setStretch(1, 1)
        self.verticalLayout.addLayout(self.horizontalLayout_4)
        self.horizontalLayout_37 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_37.setObjectName("horizontalLayout_37")
        self.btnLockKey = QtWidgets.QPushButton(DialogSettings)
        font = QtGui.QFont()
        font.setFamily("PingFang")
        self.btnLockKey.setFont(font)
        self.btnLockKey.setObjectName("btnLockKey")
        self.horizontalLayout_37.addWidget(self.btnLockKey)
        self.btnUnlockKey = QtWidgets.QPushButton(DialogSettings)
        font = QtGui.QFont()
        font.setFamily("PingFang")
        self.btnUnlockKey.setFont(font)
        self.btnUnlockKey.setObjectName("btnUnlockKey")
        self.horizontalLayout_37.addWidget(self.btnUnlockKey)
        self.horizontalLayout_37.setStretch(0, 1)
        self.horizontalLayout_37.setStretch(1, 1)
        self.verticalLayout.addLayout(self.horizontalLayout_37)
        self.horizontalLayout_43 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_43.setObjectName("horizontalLayout_43")
        self.label_49 = QtWidgets.QLabel(DialogSettings)
        self.label_49.setMinimumSize(QtCore.QSize(0, 28))
        font = QtGui.QFont()
        font.setFamily("PingFang")
        self.label_49.setFont(font)
        self.label_49.setAlignment(QtCore.Qt.AlignCenter)
        self.label_49.setObjectName("label_49")
        self.horizontalLayout_43.addWidget(self.label_49)
        self.frame_2 = QtWidgets.QFrame(DialogSettings)
        self.frame_2.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_2.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_2.setObjectName("frame_2")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(self.frame_2)
        self.verticalLayout_2.setContentsMargins(2, 2, 2, 2)
        self.verticalLayout_2.setSpacing(0)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.checkBoxOutputWarn = QtWidgets.QCheckBox(self.frame_2)
        self.checkBoxOutputWarn.setMinimumSize(QtCore.QSize(0, 28))
        self.checkBoxOutputWarn.setObjectName("checkBoxOutputWarn")
        self.verticalLayout_2.addWidget(self.checkBoxOutputWarn)
        self.checkBoxSetLock = QtWidgets.QCheckBox(self.frame_2)
        self.checkBoxSetLock.setMinimumSize(QtCore.QSize(0, 28))
        self.checkBoxSetLock.setObjectName("checkBoxSetLock")
        self.verticalLayout_2.addWidget(self.checkBoxSetLock)
        self.horizontalLayout_43.addWidget(self.frame_2)
        self.horizontalLayout_43.setStretch(0, 1)
        self.horizontalLayout_43.setStretch(1, 1)
        self.verticalLayout.addLayout(self.horizontalLayout_43)
        self.line_2 = QtWidgets.QFrame(DialogSettings)
        self.line_2.setFrameShadow(QtWidgets.QFrame.Plain)
        self.line_2.setFrameShape(QtWidgets.QFrame.HLine)
        self.line_2.setObjectName("line_2")
        self.verticalLayout.addWidget(self.line_2)
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_3.setContentsMargins(-1, -1, -1, 0)
        self.horizontalLayout_3.setSpacing(6)
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        self.btnOk = QtWidgets.QPushButton(DialogSettings)
        font = QtGui.QFont()
        font.setFamily("PingFang")
        self.btnOk.setFont(font)
        self.btnOk.setObjectName("btnOk")
        self.horizontalLayout_3.addWidget(self.btnOk)
        self.horizontalLayout_3.setStretch(0, 1)
        self.verticalLayout.addLayout(self.horizontalLayout_3)

        self.retranslateUi(DialogSettings)
        QtCore.QMetaObject.connectSlotsByName(DialogSettings)

    def retranslateUi(self, DialogSettings):
        _translate = QtCore.QCoreApplication.translate
        DialogSettings.setWindowTitle(_translate("DialogSettings", "系统设置"))
        self.label_36.setText(_translate("DialogSettings", "- 连接设置 -"))
        self.label_34.setText(_translate("DialogSettings", "串口号"))
        self.comboBoxPort.setCurrentText(_translate("DialogSettings", "自动"))
        self.comboBoxPort.setItemText(0, _translate("DialogSettings", "自动"))
        self.label_44.setText(_translate("DialogSettings", "- 系统设置 -"))
        self.Chinese_rb.setText(_translate("DialogSettings", "中文"))
        self.English_rb.setText(_translate("DialogSettings", "English"))
        self.btnSetMaxP.setText(_translate("DialogSettings", "设置最大输出功率"))
        self.spinBoxMaxP.setSuffix(_translate("DialogSettings", " W"))
        self.btnCali1.setText(_translate("DialogSettings", "触发输出校准 (7V)"))
        self.btnCali2.setText(_translate("DialogSettings", "触发输入校准 (20V)"))
        self.btnEnableShortProtect.setText(_translate("DialogSettings", "开启短路保护"))
        self.btnDisableShortProtect.setText(_translate("DialogSettings", "关闭短路保护"))
        self.btnLockKey.setText(_translate("DialogSettings", "锁定设备按键"))
        self.btnUnlockKey.setText(_translate("DialogSettings", "解锁设备按键"))
        self.label_49.setText(_translate("DialogSettings", "其它设置"))
        self.checkBoxOutputWarn.setToolTip(_translate("DialogSettings", "开启输出前显示警告"))
        self.checkBoxOutputWarn.setText(_translate("DialogSettings", "输出开启警告"))
        self.checkBoxSetLock.setToolTip(_translate("DialogSettings", "输出状态下不允许包括辅助功能在内的设定值修改操作"))
        self.checkBoxSetLock.setText(_translate("DialogSettings", "输出时锁定参数"))
        self.btnOk.setText(_translate("DialogSettings", "保存 / OK"))
