#!/bin/bash
# UI Compilation Script for Linux/macOS
# Compiles .ui files to .py files using PyQt5 tools

set -e

echo "=== UI Compilation Script ==="
echo "Compiling .ui files to .py files for WPX-QT-GUI"
echo

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# Check if pyuic5 is available
if ! command -v pyuic5 &> /dev/null; then
    if ! python3 -m PyQt5.uic.pyuic --help &> /dev/null; then
        echo "ERROR: pyuic5 not found. Please install PyQt5-tools:"
        echo "  pip install PyQt5-tools"
        exit 1
    else
        PYUIC_CMD="python3 -m PyQt5.uic.pyuic"
        echo "Found PyQt5.uic.pyuic module"
    fi
else
    PYUIC_CMD="pyuic5"
    echo "Found pyuic5"
fi

echo "Proceeding with compilation..."
echo

# UI files to compile
declare -A UI_FILES=(
    ["mdp_gui_template/mainwindow.ui"]="mdp_gui_template/mainwindow_ui.py"
    ["mdp_gui_template/settings.ui"]="mdp_gui_template/settings_ui.py"
    ["mdp_gui_template/updates.ui"]="mdp_gui_template/updates_ui.py"
    ["mdp_gui_template/graphics.ui"]="mdp_gui_template/graphics_ui.py"
)

SUCCESS_COUNT=0
TOTAL_COUNT=${#UI_FILES[@]}

# Compile each UI file
for ui_file in "${!UI_FILES[@]}"; do
    py_file="${UI_FILES[$ui_file]}"
    
    if [[ ! -f "$ui_file" ]]; then
        echo "WARNING: $ui_file not found, skipping..."
        continue
    fi
    
    echo "Compiling $ui_file -> $py_file"
    
    if $PYUIC_CMD "$ui_file" -o "$py_file"; then
        echo "  ✓ Successfully compiled $ui_file"
        ((SUCCESS_COUNT++))
    else
        echo "  ✗ Failed to compile $ui_file"
    fi
done

echo
echo "=== Compilation Summary ==="
echo "Successfully compiled: $SUCCESS_COUNT/$TOTAL_COUNT files"

if [[ $SUCCESS_COUNT -eq $TOTAL_COUNT ]]; then
    echo "✓ All UI files compiled successfully!"
else
    echo "✗ $((TOTAL_COUNT - SUCCESS_COUNT)) files failed to compile"
    exit 1
fi