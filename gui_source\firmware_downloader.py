import os
import sys
from typing import Optional, Callable
from urllib.request import urlopen, Request
from urllib.error import URLError, HTTPError

try:
    from loguru import logger
except ImportError:
    # 如果loguru不可用，使用标准logging
    import logging
    logger = logging.getLogger(__name__)
    logger.info = logger.info
    logger.error = logger.error
    logger.warning = logger.warning


class FirmwareDownloader:
    """固件下载器"""
    
    def __init__(self, memory_only: bool = False):
        self.firmware_url = "http://ee-lab.cn/Firmware/firmwares/WPX_MPP_V2.bin"
        self.local_firmware_path = "./WPX_MPP_V2.bin"
        self.progress_callback: Optional[Callable[[int], None]] = None
        self.status_callback: Optional[Callable[[str], None]] = None
        self.memory_only = memory_only  # 是否仅使用内存模式
        self.firmware_data: Optional[bytes] = None  # 内存中的固件数据
    
    def set_progress_callback(self, callback: Callable[[int], None]):
        """设置进度回调函数"""
        self.progress_callback = callback
    
    def set_status_callback(self, callback: Callable[[str], None]):
        """设置状态回调函数"""
        self.status_callback = callback
    
    def _update_status(self, message: str):
        """更新状态信息（线程安全版本）"""
        logger.info(message)
        if self.status_callback:
            try:
                # 检查当前线程，使用Qt信号机制安全更新UI
                import threading
                if threading.current_thread().name == 'MainThread':
                    self.status_callback(message)
                else:
                    # 工作线程中，使用QTimer调度到主线程
                    from PyQt5.QtCore import QTimer
                    from functools import partial
                    QTimer.singleShot(0, partial(self._safe_status_update, message))
            except Exception as e:
                logger.error(f"Status callback error: {e}")
    
    def _safe_status_update(self, message: str):
        """安全的状态更新回调"""
        try:
            if self.status_callback:
                self.status_callback(message)
        except Exception as e:
            logger.debug(f"Safe status update error: {e}")
    
    def _update_progress(self, progress: int):
        """更新下载进度（线程安全版本）"""
        logger.info(f"Firmware download progress: {progress}%")
        if self.progress_callback:
            try:
                # 检查当前线程，使用Qt信号机制安全更新UI
                import threading
                if threading.current_thread().name == 'MainThread':
                    self.progress_callback(progress)
                else:
                    # 工作线程中，使用QTimer调度到主线程
                    from PyQt5.QtCore import QTimer
                    from functools import partial
                    QTimer.singleShot(0, partial(self._safe_progress_update, progress))
            except Exception as e:
                logger.error(f"Progress callback error: {e}")
        else:
            logger.warning("No download progress callback set")
    
    def _safe_progress_update(self, progress: int):
        """安全的进度更新回调"""
        try:
            if self.progress_callback:
                self.progress_callback(progress)
        except Exception as e:
            logger.debug(f"Safe progress update error: {e}")
    
    def download_firmware(self, force_download: bool = False, max_retries: int = 3) -> bool:
        """
        下载固件文件，支持重试机制
        
        Args:
            force_download: 是否强制重新下载，即使本地已存在
            max_retries: 最大重试次数，默认3次
            
        Returns:
            bool: 下载成功返回True，失败返回False
        """
        for attempt in range(max_retries):
            try:
                if attempt > 0:
                    self._update_status(f"第{attempt + 1}次尝试下载固件...")
                    logger.info(f"Firmware download retry attempt {attempt + 1}/{max_retries}")
                
                result = self._download_firmware_attempt(force_download)
                if result:
                    return True
                    
            except Exception as e:
                logger.error(f"Download attempt {attempt + 1} failed: {e}")
                if attempt < max_retries - 1:
                    self._update_status(f"下载失败，3秒后重试...")
                    import time
                    time.sleep(3)  # 等待3秒后重试
                else:
                    # 最后一次尝试失败，显示网络异常提示
                    self._update_status("固件下载失败：网络连接异常，请检查网络连接")
                    logger.error(f"All {max_retries} download attempts failed")
                    return False
        
        return False
    
    def _download_firmware_attempt(self, force_download: bool = False) -> bool:
        """
        单次固件下载尝试
        
        Args:
            force_download: 是否强制重新下载
            
        Returns:
            bool: 下载成功返回True，失败返回False
        """
        try:
            # 检查是否需要下载
            if not self.memory_only and os.path.exists(self.local_firmware_path) and not force_download:
                self._update_status("本地固件文件已存在，跳过下载")
                return True
            
            if self.memory_only and self.firmware_data is not None and not force_download:
                self._update_status("固件已在内存中，跳过下载")
                return True
            
            self._update_status("开始下载固件...")
            
            # 创建HTTP请求，设置更短的超时
            request = Request(self.firmware_url)
            request.add_header('User-Agent', 'PD-Pocket-Controller/1.0')
            
            # 发送HTTP请求下载固件，设置较短的连接超时
            try:
                response = urlopen(request, timeout=15)  # 增加到15秒以提高成功率
            except Exception as conn_error:
                logger.error(f"Connection failed: {conn_error}")
                self._update_status(f"连接失败: {conn_error}")
                raise
            
            # 获取文件大小
            total_size = int(response.headers.get('Content-Length', 0))
            if total_size == 0:
                # 如果无法获取Content-Length，尝试下载并计算大小
                self._update_status("开始下载固件（未知大小）...")
            else:
                self._update_status(f"固件文件大小: {total_size / 1024 / 1024:.2f} MB")
            
            # 使用内存缓存下载文件，避免文件流导致的卡机问题
            self._update_status("正在下载到内存缓存...")
            firmware_data = bytearray()
            downloaded_size = 0
            chunk_size = 8192  # 8KB chunks
            
            # 设置较短的socket超时
            import socket
            try:
                response.fp.raw._sock.settimeout(8)  # 增加到8秒以提高稳定性
            except AttributeError:
                # 如果无法设置socket超时，继续执行
                logger.debug("Could not set socket timeout, continuing anyway")
                pass
            
            # 使用合理的chunk size，平衡性能和稳定性
            chunk_size = 8192  # 8KB chunks，平衡性能和稳定性
            timeout_count = 0
            max_timeouts = 5  # 增加容错性
            
            while True:
                try:
                    chunk = response.read(chunk_size)
                    if not chunk:
                        break
                    
                    # 安全的内存操作，防止内存不足导致闪退
                    try:
                        firmware_data.extend(chunk)
                        downloaded_size += len(chunk)
                        timeout_count = 0  # 重置超时计数
                    except MemoryError:
                        logger.error("Memory error during firmware download")
                        self._update_status("下载失败：内存不足")
                        return False
                    
                    # 适度的回调频率，保持用户体验
                    if total_size > 0:
                        progress = int((downloaded_size / total_size) * 100)
                        # 只在进度变化时更新（每1%更新一次）
                        if not hasattr(self, '_last_progress') or progress != self._last_progress:
                            self._last_progress = progress
                            self._update_progress(progress)
                        
                        # 减少状态更新频率
                        if downloaded_size % (200 * 1024) == 0:  # 每200KB更新一次
                            self._update_status(f"下载进度: {progress}%")
                    else:
                        # 如果不知道总大小，减少状态更新
                        if downloaded_size % (500 * 1024) == 0:  # 每500KB更新一次状态
                            self._update_status(f"已下载: {downloaded_size // 1024} KB")
                        
                except socket.timeout:
                    timeout_count += 1
                    logger.warning(f"Socket timeout #{timeout_count} during firmware download")
                    self._update_status(f"网络超时（第{timeout_count}次），继续下载...")
                    if timeout_count >= max_timeouts:
                        logger.error(f"Too many timeouts ({timeout_count}), will retry entire download")
                        self._update_status(f"网络超时次数过多，将重新尝试下载")
                        raise Exception(f"Too many socket timeouts: {timeout_count}")
                    continue  # 继续下载
                except MemoryError:
                    logger.error("Memory error during firmware download")
                    self._update_status("下载失败：内存不足")
                    raise  # 重新抛出异常，但不重试内存错误
                except Exception as chunk_error:
                    logger.error(f"Download chunk error: {chunk_error}")
                    self._update_status(f"下载出错: {str(chunk_error)}")
                    raise  # 重新抛出异常以触发重试
            
            # 保存固件数据
            self.firmware_data = bytes(firmware_data)
            
            if self.memory_only:
                # 纯内存模式，不保存到文件
                response.close()
                self._update_status("固件下载完成（保存在内存中）")
                return True
            else:
                # 传统模式，保存到文件
                self._update_status("下载完成，正在保存到本地...")
                with open(self.local_firmware_path, 'wb') as file:
                    file.write(firmware_data)
                
                response.close()
                self._update_status("固件下载完成")
                return True
            
        except HTTPError as e:
            error_msg = f"HTTP错误: {e.code} - {e.reason}"
            if e.code in [404, 403]:
                error_msg += " (固件文件不存在或无权访问)"
            elif e.code >= 500:
                error_msg += " (服务器错误)"
            self._update_status(error_msg)
            logger.error(f"HTTP error during firmware download: {e.code} - {e.reason}")
            raise  # 重新抛出异常以触发重试
        except URLError as e:
            error_msg = f"网络连接错误: {e.reason}"
            # 检查是否是网络不可达的问题
            reason_str = str(e.reason).lower()
            if 'timeout' in reason_str or 'timed out' in reason_str:
                error_msg += " (连接超时)"
            elif 'name resolution' in reason_str or 'nodename' in reason_str:
                error_msg += " (DNS解析失败)"
            elif 'connection refused' in reason_str:
                error_msg += " (连接被拒绝)"
            self._update_status(error_msg)
            logger.error(f"URL error during firmware download: {e.reason}")
            raise  # 重新抛出异常以触发重试
        except Exception as e:
            error_msg = f"下载失败: {str(e)}"
            # 检查是否是内存错误
            if 'memory' in str(e).lower():
                error_msg += " (内存不足)"
            elif 'timeout' in str(e).lower():
                error_msg += " (超时)"
            self._update_status(error_msg)
            logger.error(f"Firmware download error: {e}")
            raise  # 重新抛出异常以触发重试
    
    def get_firmware_path(self) -> str:
        """获取本地固件文件路径"""
        return self.local_firmware_path
    
    def get_firmware_data(self) -> Optional[bytes]:
        """获取内存中的固件数据"""
        return self.firmware_data
    
    def is_firmware_available(self) -> bool:
        """检查是否有可用的固件"""
        if self.memory_only:
            return self.firmware_data is not None
        else:
            return os.path.exists(self.local_firmware_path) or self.firmware_data is not None
    
    def get_firmware_info(self) -> dict:
        """获取固件信息（线程安全版本）"""
        if not self.is_firmware_available():
            return {"exists": False}
        
        try:
            if self.memory_only and self.firmware_data is not None:
                # 内存模式，从内存数据获取信息
                size = len(self.firmware_data)
                return {
                    "exists": True,
                    "size": size,
                    "path": "内存中",
                    "size_mb": size / 1024 / 1024,
                    "memory_mode": True
                }
            elif os.path.exists(self.local_firmware_path):
                # 文件模式，从文件获取信息
                stat = os.stat(self.local_firmware_path)
                return {
                    "exists": True,
                    "size": stat.st_size,
                    "path": self.local_firmware_path,
                    "size_mb": stat.st_size / 1024 / 1024,
                    "memory_mode": False
                }
            else:
                return {"exists": False}
        except Exception as e:
            logger.error(f"Error getting firmware info: {e}")
            return {"exists": False}


if __name__ == "__main__":
    # 测试代码
    def progress_callback(progress):
        print(f"下载进度: {progress}%")
    
    def status_callback(status):
        print(f"状态: {status}")
    
    downloader = FirmwareDownloader()
    downloader.set_progress_callback(progress_callback)
    downloader.set_status_callback(status_callback)
    
    success = downloader.download_firmware()
    if success:
        info = downloader.get_firmware_info()
        print(f"固件信息: {info}")