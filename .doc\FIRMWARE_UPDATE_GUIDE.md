# 固件更新功能说明

## 功能概述
新增了一键固件下载并自动烧录功能，支持从指定URL自动下载WPX固件并直接烧录到设备。

## 使用方法

### 1. 启动固件更新
点击界面上的 **"固件更新"** 按钮

### 2. 选择更新方式
系统会弹出选择对话框，提供两种更新方式：

- **本地文件**: 传统方式，选择本地的固件文件
- **一键下载**: 新功能，自动从服务器下载最新固件

### 3. 一键下载模式流程
选择"一键下载"后：

1. **选择串口**: 系统自动检测可用串口，选择连接设备的串口
2. **自动下载**: 从 `http://ee-lab.cn/Firmware/firmwares/WPX_MPP_V2.bin` 下载固件
3. **显示进度**: 实时显示下载进度
4. **自动烧录**: 下载完成后自动开始固件烧录
5. **烧录进度**: 显示烧录进度和状态信息

## 技术特性
- **内存缓存**: 固件直接下载到内存，无需创建临时文件，提高处理速度
- **实时进度**: 实时显示下载和烧录进度，8KB分块下载确保响应性
- **错误处理**: 完善的错误提示和恢复机制
- **无文件IO**: 避免临时文件创建导致的卡顿问题
- **线程安全**: 使用独立线程处理下载和烧录，不阻塞界面
- **内存传输**: 自定义内存版Ymodem传输，直接操作字节数据

## 注意事项
1. 确保设备已正确连接并识别为串口设备
2. 确保网络连接正常，可以访问固件服务器
3. 固件烧录过程中请勿断开设备连接
4. 如果遇到串口权限问题，请以管理员权限运行程序

## 文件修改
主要修改了以下文件：
- `gui_source/mdp_gui.py`: 新增一键下载和烧录功能

## 依赖项
- Python 标准库: `urllib.request`, `tempfile`, `threading`
- 现有依赖: `PyQt5`, `serial`, `ymodem_update`

## 固件信息
- **固件名称**: WPX_MPP_V2.bin
- **下载地址**: http://ee-lab.cn/Firmware/firmwares/WPX_MPP_V2.bin
- **文件大小**: 约960KB
- **传输协议**: Ymodem