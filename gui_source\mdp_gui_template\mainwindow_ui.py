# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'mdp_gui_template\mainwindow.ui'
#
# Created by: PyQt5 UI code generator 5.15.11
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_MainWindow(object):
    def setupUi(self, MainWindow):
        MainWindow.setObjectName("MainWindow")
        MainWindow.resize(1374, 975)
        MainWindow.setMinimumSize(QtCore.QSize(0, 0))
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap("mdp_gui_template\\../icon.ico"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        MainWindow.setWindowIcon(icon)
        self.centralwidget = QtWidgets.QWidget(MainWindow)
        self.centralwidget.setObjectName("centralwidget")
        self.verticalLayout_8 = QtWidgets.QVBoxLayout(self.centralwidget)
        self.verticalLayout_8.setObjectName("verticalLayout_8")
        spacerItem = QtWidgets.QSpacerItem(20, 30, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        self.verticalLayout_8.addItem(spacerItem)
        self.verticalLayout_23 = QtWidgets.QVBoxLayout()
        self.verticalLayout_23.setObjectName("verticalLayout_23")
        self.verticalLayout_8.addLayout(self.verticalLayout_23)
        self.horizontalLayout_13 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_13.setObjectName("horizontalLayout_13")
        self.verticalLayout_12 = QtWidgets.QVBoxLayout()
        self.verticalLayout_12.setContentsMargins(-1, -1, 0, -1)
        self.verticalLayout_12.setSpacing(6)
        self.verticalLayout_12.setObjectName("verticalLayout_12")
        self.horizontalLayout_13.addLayout(self.verticalLayout_12)
        self.horizontalLayout_bottom = QtWidgets.QHBoxLayout()
        self.horizontalLayout_bottom.setSpacing(10)
        self.horizontalLayout_bottom.setObjectName("horizontalLayout_bottom")
        self.frameGraph = QtWidgets.QFrame(self.centralwidget)
        self.frameGraph.setEnabled(True)
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei")
        self.frameGraph.setFont(font)
        self.frameGraph.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frameGraph.setFrameShadow(QtWidgets.QFrame.Plain)
        self.frameGraph.setLineWidth(2)
        self.frameGraph.setObjectName("frameGraph")
        self.verticalLayout_18 = QtWidgets.QVBoxLayout(self.frameGraph)
        self.verticalLayout_18.setContentsMargins(3, 3, 3, 3)
        self.verticalLayout_18.setObjectName("verticalLayout_18")
        self.frameLcd = QtWidgets.QFrame(self.frameGraph)
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei")
        self.frameLcd.setFont(font)
        self.frameLcd.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frameLcd.setFrameShadow(QtWidgets.QFrame.Plain)
        self.frameLcd.setLineWidth(2)
        self.frameLcd.setObjectName("frameLcd")
        self.verticalLayout_19 = QtWidgets.QVBoxLayout(self.frameLcd)
        self.verticalLayout_19.setContentsMargins(6, 6, 6, 6)
        self.verticalLayout_19.setObjectName("verticalLayout_19")
        self.horizontalLayout_7 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_7.setSpacing(0)
        self.horizontalLayout_7.setObjectName("horizontalLayout_7")
        self.verticalLayout = QtWidgets.QVBoxLayout()
        self.verticalLayout.setSpacing(10)
        self.verticalLayout.setObjectName("verticalLayout")
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.label = QtWidgets.QLabel(self.frameLcd)
        self.label.setMinimumSize(QtCore.QSize(0, 50))
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei")
        font.setPointSize(13)
        font.setBold(True)
        font.setWeight(75)
        self.label.setFont(font)
        self.label.setScaledContents(False)
        self.label.setAlignment(QtCore.Qt.AlignCenter)
        self.label.setObjectName("label")
        self.horizontalLayout.addWidget(self.label)
        self.lcdVoltage = AppleStyleDisplay(self.frameLcd)
        self.lcdVoltage.setEnabled(True)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Ignored, QtWidgets.QSizePolicy.Minimum)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.lcdVoltage.sizePolicy().hasHeightForWidth())
        self.lcdVoltage.setSizePolicy(sizePolicy)
        self.lcdVoltage.setMinimumSize(QtCore.QSize(200, 50))
        self.lcdVoltage.setMaximumSize(QtCore.QSize(280, 60))
        self.lcdVoltage.setObjectName("lcdVoltage")
        self.horizontalLayout.addWidget(self.lcdVoltage)
        self.horizontalLayout.setStretch(0, 2)
        self.horizontalLayout.setStretch(1, 5)
        self.verticalLayout.addLayout(self.horizontalLayout)
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.label_2 = QtWidgets.QLabel(self.frameLcd)
        self.label_2.setMinimumSize(QtCore.QSize(0, 50))
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei")
        font.setPointSize(13)
        font.setBold(True)
        font.setWeight(75)
        self.label_2.setFont(font)
        self.label_2.setScaledContents(False)
        self.label_2.setAlignment(QtCore.Qt.AlignCenter)
        self.label_2.setObjectName("label_2")
        self.horizontalLayout_2.addWidget(self.label_2)
        self.lcdCurrent = AppleStyleDisplay(self.frameLcd)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Ignored, QtWidgets.QSizePolicy.Minimum)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.lcdCurrent.sizePolicy().hasHeightForWidth())
        self.lcdCurrent.setSizePolicy(sizePolicy)
        self.lcdCurrent.setMinimumSize(QtCore.QSize(200, 50))
        self.lcdCurrent.setMaximumSize(QtCore.QSize(280, 60))
        self.lcdCurrent.setObjectName("lcdCurrent")
        self.horizontalLayout_2.addWidget(self.lcdCurrent)
        self.horizontalLayout_2.setStretch(0, 2)
        self.horizontalLayout_2.setStretch(1, 5)
        self.verticalLayout.addLayout(self.horizontalLayout_2)
        self.horizontalLayout_7.addLayout(self.verticalLayout)
        self.verticalLayout_2 = QtWidgets.QVBoxLayout()
        self.verticalLayout_2.setSpacing(10)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        self.label_3 = QtWidgets.QLabel(self.frameLcd)
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei")
        font.setPointSize(13)
        font.setBold(True)
        font.setWeight(75)
        self.label_3.setFont(font)
        self.label_3.setScaledContents(False)
        self.label_3.setAlignment(QtCore.Qt.AlignCenter)
        self.label_3.setObjectName("label_3")
        self.horizontalLayout_3.addWidget(self.label_3)
        self.lcdPower = AppleStyleDisplay(self.frameLcd)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Ignored, QtWidgets.QSizePolicy.Minimum)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.lcdPower.sizePolicy().hasHeightForWidth())
        self.lcdPower.setSizePolicy(sizePolicy)
        self.lcdPower.setMinimumSize(QtCore.QSize(200, 50))
        self.lcdPower.setMaximumSize(QtCore.QSize(280, 60))
        self.lcdPower.setObjectName("lcdPower")
        self.horizontalLayout_3.addWidget(self.lcdPower)
        self.horizontalLayout_3.setStretch(0, 2)
        self.horizontalLayout_3.setStretch(1, 5)
        self.verticalLayout_2.addLayout(self.horizontalLayout_3)
        self.horizontalLayout_4 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_4.setObjectName("horizontalLayout_4")
        self.label_4 = QtWidgets.QLabel(self.frameLcd)
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei")
        font.setPointSize(13)
        font.setBold(True)
        font.setWeight(75)
        self.label_4.setFont(font)
        self.label_4.setScaledContents(False)
        self.label_4.setAlignment(QtCore.Qt.AlignCenter)
        self.label_4.setIndent(-1)
        self.label_4.setObjectName("label_4")
        self.horizontalLayout_4.addWidget(self.label_4)
        self.lcdEnerge = AppleStyleDisplay(self.frameLcd)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Ignored, QtWidgets.QSizePolicy.Minimum)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.lcdEnerge.sizePolicy().hasHeightForWidth())
        self.lcdEnerge.setSizePolicy(sizePolicy)
        self.lcdEnerge.setMinimumSize(QtCore.QSize(200, 50))
        self.lcdEnerge.setMaximumSize(QtCore.QSize(280, 60))
        self.lcdEnerge.setObjectName("lcdEnerge")
        self.horizontalLayout_4.addWidget(self.lcdEnerge)
        self.horizontalLayout_4.setStretch(0, 2)
        self.horizontalLayout_4.setStretch(1, 5)
        self.verticalLayout_2.addLayout(self.horizontalLayout_4)
        self.horizontalLayout_7.addLayout(self.verticalLayout_2)
        self.verticalLayout_3 = QtWidgets.QVBoxLayout()
        self.verticalLayout_3.setSpacing(10)
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.horizontalLayout_5 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_5.setObjectName("horizontalLayout_5")
        self.label_5 = QtWidgets.QLabel(self.frameLcd)
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei")
        font.setPointSize(13)
        font.setBold(True)
        font.setWeight(75)
        self.label_5.setFont(font)
        self.label_5.setScaledContents(False)
        self.label_5.setAlignment(QtCore.Qt.AlignCenter)
        self.label_5.setObjectName("label_5")
        self.horizontalLayout_5.addWidget(self.label_5)
        self.lcdAvgPower = AppleStyleDisplay(self.frameLcd)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Ignored, QtWidgets.QSizePolicy.Minimum)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.lcdAvgPower.sizePolicy().hasHeightForWidth())
        self.lcdAvgPower.setSizePolicy(sizePolicy)
        self.lcdAvgPower.setMinimumSize(QtCore.QSize(200, 50))
        self.lcdAvgPower.setMaximumSize(QtCore.QSize(280, 60))
        self.lcdAvgPower.setObjectName("lcdAvgPower")
        self.horizontalLayout_5.addWidget(self.lcdAvgPower)
        self.horizontalLayout_5.setStretch(0, 2)
        self.horizontalLayout_5.setStretch(1, 5)
        self.verticalLayout_3.addLayout(self.horizontalLayout_5)
        self.horizontalLayout_6 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_6.setObjectName("horizontalLayout_6")
        self.label_6 = QtWidgets.QLabel(self.frameLcd)
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei")
        font.setPointSize(13)
        font.setBold(True)
        font.setWeight(75)
        self.label_6.setFont(font)
        self.label_6.setScaledContents(False)
        self.label_6.setAlignment(QtCore.Qt.AlignCenter)
        self.label_6.setObjectName("label_6")
        self.horizontalLayout_6.addWidget(self.label_6)
        self.lcdResistence = AppleStyleDisplay(self.frameLcd)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Ignored, QtWidgets.QSizePolicy.Minimum)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.lcdResistence.sizePolicy().hasHeightForWidth())
        self.lcdResistence.setSizePolicy(sizePolicy)
        self.lcdResistence.setMinimumSize(QtCore.QSize(200, 50))
        self.lcdResistence.setMaximumSize(QtCore.QSize(280, 60))
        self.lcdResistence.setObjectName("lcdResistence")
        self.horizontalLayout_6.addWidget(self.lcdResistence)
        self.horizontalLayout_6.setStretch(0, 2)
        self.horizontalLayout_6.setStretch(1, 5)
        self.verticalLayout_3.addLayout(self.horizontalLayout_6)
        self.horizontalLayout_7.addLayout(self.verticalLayout_3)
        self.horizontalLayout_7.setStretch(0, 1)
        self.horizontalLayout_7.setStretch(1, 1)
        self.horizontalLayout_7.setStretch(2, 1)
        self.verticalLayout_19.addLayout(self.horizontalLayout_7)
        self.verticalLayout_18.addWidget(self.frameLcd)
        self.verticalLayout_7 = QtWidgets.QVBoxLayout()
        self.verticalLayout_7.setContentsMargins(3, 8, 3, 6)
        self.verticalLayout_7.setSpacing(6)
        self.verticalLayout_7.setObjectName("verticalLayout_7")
        self.horizontalLayout_27 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_27.setContentsMargins(-1, 12, -1, 12)
        self.horizontalLayout_27.setObjectName("horizontalLayout_27")
        self.label_13 = QtWidgets.QLabel(self.frameGraph)
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei")
        self.label_13.setFont(font)
        self.label_13.setAlignment(QtCore.Qt.AlignCenter)
        self.label_13.setObjectName("label_13")
        self.horizontalLayout_27.addWidget(self.label_13)
        self.comboGraph1Data = QtWidgets.QComboBox(self.frameGraph)
        self.comboGraph1Data.setMinimumSize(QtCore.QSize(90, 0))
        self.comboGraph1Data.setMaximumSize(QtCore.QSize(90, 16777215))
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei")
        self.comboGraph1Data.setFont(font)
        self.comboGraph1Data.setObjectName("comboGraph1Data")
        self.comboGraph1Data.addItem("")
        self.comboGraph1Data.addItem("")
        self.comboGraph1Data.addItem("")
        self.comboGraph1Data.addItem("")
        self.comboGraph1Data.addItem("")
        self.comboGraph1Data.addItem("")
        self.comboGraph1Data.addItem("")
        self.comboGraph1Data.addItem("")
        self.comboGraph1Data.addItem("")
        self.horizontalLayout_27.addWidget(self.comboGraph1Data)
        self.comboGraph2Data = QtWidgets.QComboBox(self.frameGraph)
        self.comboGraph2Data.setMinimumSize(QtCore.QSize(90, 0))
        self.comboGraph2Data.setMaximumSize(QtCore.QSize(90, 16777215))
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei")
        self.comboGraph2Data.setFont(font)
        self.comboGraph2Data.setObjectName("comboGraph2Data")
        self.comboGraph2Data.addItem("")
        self.comboGraph2Data.addItem("")
        self.comboGraph2Data.addItem("")
        self.comboGraph2Data.addItem("")
        self.comboGraph2Data.addItem("")
        self.comboGraph2Data.addItem("")
        self.comboGraph2Data.addItem("")
        self.comboGraph2Data.addItem("")
        self.comboGraph2Data.addItem("")
        self.horizontalLayout_27.addWidget(self.comboGraph2Data)
        self.label_29 = QtWidgets.QLabel(self.frameGraph)
        self.label_29.setMinimumSize(QtCore.QSize(25, 0))
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei")
        self.label_29.setFont(font)
        self.label_29.setAlignment(QtCore.Qt.AlignCenter)
        self.label_29.setObjectName("label_29")
        self.horizontalLayout_27.addWidget(self.label_29)
        self.labelFps = QtWidgets.QLabel(self.frameGraph)
        self.labelFps.setMinimumSize(QtCore.QSize(50, 0))
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei")
        self.labelFps.setFont(font)
        self.labelFps.setAlignment(QtCore.Qt.AlignCenter)
        self.labelFps.setObjectName("labelFps")
        self.horizontalLayout_27.addWidget(self.labelFps)
        spacerItem1 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_27.addItem(spacerItem1)
        self.btnGraphRecord = QtWidgets.QPushButton(self.frameGraph)
        self.btnGraphRecord.setMinimumSize(QtCore.QSize(60, 30))
        self.btnGraphRecord.setMaximumSize(QtCore.QSize(80, 16777215))
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei")
        self.btnGraphRecord.setFont(font)
        self.btnGraphRecord.setObjectName("btnGraphRecord")
        self.horizontalLayout_27.addWidget(self.btnGraphRecord)
        self.btnGraphDump = QtWidgets.QPushButton(self.frameGraph)
        self.btnGraphDump.setMinimumSize(QtCore.QSize(60, 30))
        self.btnGraphDump.setMaximumSize(QtCore.QSize(80, 16777215))
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei")
        self.btnGraphDump.setFont(font)
        self.btnGraphDump.setObjectName("btnGraphDump")
        self.horizontalLayout_27.addWidget(self.btnGraphDump)
        self.btnGraphAutoScale = QtWidgets.QPushButton(self.frameGraph)
        self.btnGraphAutoScale.setMinimumSize(QtCore.QSize(60, 30))
        self.btnGraphAutoScale.setMaximumSize(QtCore.QSize(80, 16777215))
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei")
        self.btnGraphAutoScale.setFont(font)
        self.btnGraphAutoScale.setObjectName("btnGraphAutoScale")
        self.horizontalLayout_27.addWidget(self.btnGraphAutoScale)
        self.btnRecordFloatWindow = QtWidgets.QPushButton(self.frameGraph)
        self.btnRecordFloatWindow.setMinimumSize(QtCore.QSize(60, 30))
        self.btnRecordFloatWindow.setMaximumSize(QtCore.QSize(80, 16777215))
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei")
        self.btnRecordFloatWindow.setFont(font)
        self.btnRecordFloatWindow.setObjectName("btnRecordFloatWindow")
        self.horizontalLayout_27.addWidget(self.btnRecordFloatWindow)
        self.btnRecordClear = QtWidgets.QPushButton(self.frameGraph)
        self.btnRecordClear.setMinimumSize(QtCore.QSize(60, 30))
        self.btnRecordClear.setMaximumSize(QtCore.QSize(80, 16777215))
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei")
        self.btnRecordClear.setFont(font)
        self.btnRecordClear.setObjectName("btnRecordClear")
        self.horizontalLayout_27.addWidget(self.btnRecordClear)
        self.btnGraphClear = QtWidgets.QPushButton(self.frameGraph)
        self.btnGraphClear.setMinimumSize(QtCore.QSize(60, 30))
        self.btnGraphClear.setMaximumSize(QtCore.QSize(80, 16777215))
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei")
        self.btnGraphClear.setFont(font)
        self.btnGraphClear.setObjectName("btnGraphClear")
        self.horizontalLayout_27.addWidget(self.btnGraphClear)
        self.btnGraphKeep = QtWidgets.QPushButton(self.frameGraph)
        self.btnGraphKeep.setMinimumSize(QtCore.QSize(60, 30))
        self.btnGraphKeep.setMaximumSize(QtCore.QSize(80, 16777215))
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei")
        self.btnGraphKeep.setFont(font)
        self.btnGraphKeep.setObjectName("btnGraphKeep")
        self.horizontalLayout_27.addWidget(self.btnGraphKeep)
        self.verticalLayout_7.addLayout(self.horizontalLayout_27)
        spacerItem2 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_7.addItem(spacerItem2)
        self.frameGraphContainer = QtWidgets.QFrame(self.frameGraph)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(1)
        sizePolicy.setHeightForWidth(self.frameGraphContainer.sizePolicy().hasHeightForWidth())
        self.frameGraphContainer.setSizePolicy(sizePolicy)
        self.frameGraphContainer.setMinimumSize(QtCore.QSize(0, 100))
        self.frameGraphContainer.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.frameGraphContainer.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frameGraphContainer.setObjectName("frameGraphContainer")
        self.verticalLayoutGraphContainer = QtWidgets.QVBoxLayout(self.frameGraphContainer)
        self.verticalLayoutGraphContainer.setContentsMargins(0, 0, 0, 0)
        self.verticalLayoutGraphContainer.setSpacing(2)
        self.verticalLayoutGraphContainer.setObjectName("verticalLayoutGraphContainer")
        self.widgetGraph1 = PlotWidget(self.frameGraphContainer)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.widgetGraph1.sizePolicy().hasHeightForWidth())
        self.widgetGraph1.setSizePolicy(sizePolicy)
        self.widgetGraph1.setMinimumSize(QtCore.QSize(0, 480))
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei")
        self.widgetGraph1.setFont(font)
        self.widgetGraph1.setObjectName("widgetGraph1")
        self.verticalLayoutGraphContainer.addWidget(self.widgetGraph1)
        self.frameThumbnailContainer = QtWidgets.QFrame(self.frameGraphContainer)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.frameThumbnailContainer.sizePolicy().hasHeightForWidth())
        self.frameThumbnailContainer.setSizePolicy(sizePolicy)
        self.frameThumbnailContainer.setMinimumSize(QtCore.QSize(0, 70))
        self.frameThumbnailContainer.setMaximumSize(QtCore.QSize(16777215, 70))
        self.frameThumbnailContainer.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.frameThumbnailContainer.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frameThumbnailContainer.setObjectName("frameThumbnailContainer")
        self.horizontalLayoutThumbnail = QtWidgets.QHBoxLayout(self.frameThumbnailContainer)
        self.horizontalLayoutThumbnail.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayoutThumbnail.setSpacing(0)
        self.horizontalLayoutThumbnail.setObjectName("horizontalLayoutThumbnail")
        self.widgetThumbnailPlaceholder = QtWidgets.QWidget(self.frameThumbnailContainer)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.widgetThumbnailPlaceholder.sizePolicy().hasHeightForWidth())
        self.widgetThumbnailPlaceholder.setSizePolicy(sizePolicy)
        self.widgetThumbnailPlaceholder.setMaximumSize(QtCore.QSize(16777215, 100))
        self.widgetThumbnailPlaceholder.setObjectName("widgetThumbnailPlaceholder")
        self.horizontalLayoutThumbnail.addWidget(self.widgetThumbnailPlaceholder)
        self.verticalLayoutGraphContainer.addWidget(self.frameThumbnailContainer)
        self.verticalLayout_7.addWidget(self.frameGraphContainer)
        self.verticalLayout_18.addLayout(self.verticalLayout_7)
        self.horizontalLayout_bottom.addWidget(self.frameGraph)
        self.verticalLayout_settings = QtWidgets.QVBoxLayout()
        self.verticalLayout_settings.setSpacing(10)
        self.verticalLayout_settings.setObjectName("verticalLayout_settings")
        self.verticalLayout_121 = QtWidgets.QVBoxLayout()
        self.verticalLayout_121.setSpacing(10)
        self.verticalLayout_121.setObjectName("verticalLayout_121")
        self.verticalLayout_settings.addLayout(self.verticalLayout_121)
        self.frameOutputSetting = QtWidgets.QFrame(self.centralwidget)
        self.frameOutputSetting.setEnabled(True)
        self.frameOutputSetting.setMaximumSize(QtCore.QSize(380, 16777215))
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei")
        self.frameOutputSetting.setFont(font)
        self.frameOutputSetting.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frameOutputSetting.setFrameShadow(QtWidgets.QFrame.Plain)
        self.frameOutputSetting.setLineWidth(1)
        self.frameOutputSetting.setObjectName("frameOutputSetting")
        self.verticalLayout_11 = QtWidgets.QVBoxLayout(self.frameOutputSetting)
        self.verticalLayout_11.setContentsMargins(3, 3, 3, 3)
        self.verticalLayout_11.setObjectName("verticalLayout_11")
        self.verticalLayout_6 = QtWidgets.QVBoxLayout()
        self.verticalLayout_6.setObjectName("verticalLayout_6")
        self.gridLayout_data = QtWidgets.QGridLayout()
        self.gridLayout_data.setContentsMargins(9, 9, 9, 9)
        self.gridLayout_data.setObjectName("gridLayout_data")
        self.run_time_lb = QtWidgets.QLabel(self.frameOutputSetting)
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.run_time_lb.setFont(font)
        self.run_time_lb.setObjectName("run_time_lb")
        self.gridLayout_data.addWidget(self.run_time_lb, 2, 1, 1, 1)
        self.potential_power_lb = QtWidgets.QLabel(self.frameOutputSetting)
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.potential_power_lb.setFont(font)
        self.potential_power_lb.setObjectName("potential_power_lb")
        self.gridLayout_data.addWidget(self.potential_power_lb, 1, 4, 1, 1)
        self.line_6 = QtWidgets.QFrame(self.frameOutputSetting)
        self.line_6.setFrameShape(QtWidgets.QFrame.VLine)
        self.line_6.setFrameShadow(QtWidgets.QFrame.Sunken)
        self.line_6.setObjectName("line_6")
        self.gridLayout_data.addWidget(self.line_6, 3, 2, 1, 1)
        self.Coil_temp_bt = QtWidgets.QLabel(self.frameOutputSetting)
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.Coil_temp_bt.setFont(font)
        self.Coil_temp_bt.setAlignment(QtCore.Qt.AlignCenter)
        self.Coil_temp_bt.setObjectName("Coil_temp_bt")
        self.gridLayout_data.addWidget(self.Coil_temp_bt, 3, 0, 1, 1)
        self.line_8 = QtWidgets.QFrame(self.frameOutputSetting)
        self.line_8.setFrameShape(QtWidgets.QFrame.VLine)
        self.line_8.setFrameShadow(QtWidgets.QFrame.Sunken)
        self.line_8.setObjectName("line_8")
        self.gridLayout_data.addWidget(self.line_8, 5, 2, 1, 1)
        self.signal_strength_lb = QtWidgets.QLabel(self.frameOutputSetting)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.signal_strength_lb.sizePolicy().hasHeightForWidth())
        self.signal_strength_lb.setSizePolicy(sizePolicy)
        self.signal_strength_lb.setMinimumSize(QtCore.QSize(0, 0))
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.signal_strength_lb.setFont(font)
        self.signal_strength_lb.setObjectName("signal_strength_lb")
        self.gridLayout_data.addWidget(self.signal_strength_lb, 0, 4, 1, 1)
        self.label_16 = QtWidgets.QLabel(self.frameOutputSetting)
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.label_16.setFont(font)
        self.label_16.setAlignment(QtCore.Qt.AlignCenter)
        self.label_16.setObjectName("label_16")
        self.gridLayout_data.addWidget(self.label_16, 1, 0, 1, 1)
        self.line_7 = QtWidgets.QFrame(self.frameOutputSetting)
        self.line_7.setFrameShape(QtWidgets.QFrame.VLine)
        self.line_7.setFrameShadow(QtWidgets.QFrame.Sunken)
        self.line_7.setObjectName("line_7")
        self.gridLayout_data.addWidget(self.line_7, 4, 2, 1, 1)
        self.label_24 = QtWidgets.QLabel(self.frameOutputSetting)
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.label_24.setFont(font)
        self.label_24.setAlignment(QtCore.Qt.AlignCenter)
        self.label_24.setObjectName("label_24")
        self.gridLayout_data.addWidget(self.label_24, 4, 0, 1, 1)
        self.label_39 = QtWidgets.QLabel(self.frameOutputSetting)
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.label_39.setFont(font)
        self.label_39.setAlignment(QtCore.Qt.AlignCenter)
        self.label_39.setObjectName("label_39")
        self.gridLayout_data.addWidget(self.label_39, 3, 3, 1, 1)
        self.label_12 = QtWidgets.QLabel(self.frameOutputSetting)
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.label_12.setFont(font)
        self.label_12.setAlignment(QtCore.Qt.AlignCenter)
        self.label_12.setObjectName("label_12")
        self.gridLayout_data.addWidget(self.label_12, 0, 3, 1, 1)
        self.label_30 = QtWidgets.QLabel(self.frameOutputSetting)
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.label_30.setFont(font)
        self.label_30.setAlignment(QtCore.Qt.AlignCenter)
        self.label_30.setObjectName("label_30")
        self.gridLayout_data.addWidget(self.label_30, 5, 0, 1, 1)
        self.ext_probe_temp_lb = QtWidgets.QLabel(self.frameOutputSetting)
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.ext_probe_temp_lb.setFont(font)
        self.ext_probe_temp_lb.setObjectName("ext_probe_temp_lb")
        self.gridLayout_data.addWidget(self.ext_probe_temp_lb, 6, 4, 1, 1)
        self.label_26 = QtWidgets.QLabel(self.frameOutputSetting)
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.label_26.setFont(font)
        self.label_26.setAlignment(QtCore.Qt.AlignCenter)
        self.label_26.setObjectName("label_26")
        self.gridLayout_data.addWidget(self.label_26, 6, 3, 1, 1)
        self.line_10 = QtWidgets.QFrame(self.frameOutputSetting)
        self.line_10.setFrameShape(QtWidgets.QFrame.VLine)
        self.line_10.setFrameShadow(QtWidgets.QFrame.Sunken)
        self.line_10.setObjectName("line_10")
        self.gridLayout_data.addWidget(self.line_10, 7, 2, 1, 1)
        self.ce_lb = QtWidgets.QLabel(self.frameOutputSetting)
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.ce_lb.setFont(font)
        self.ce_lb.setObjectName("ce_lb")
        self.gridLayout_data.addWidget(self.ce_lb, 2, 4, 1, 1)
        self.rpp_lb = QtWidgets.QLabel(self.frameOutputSetting)
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.rpp_lb.setFont(font)
        self.rpp_lb.setObjectName("rpp_lb")
        self.gridLayout_data.addWidget(self.rpp_lb, 3, 4, 1, 1)
        self.coil_temp_lb = QtWidgets.QLabel(self.frameOutputSetting)
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.coil_temp_lb.setFont(font)
        self.coil_temp_lb.setObjectName("coil_temp_lb")
        self.gridLayout_data.addWidget(self.coil_temp_lb, 3, 1, 1, 1)
        self.eload_temp_lb = QtWidgets.QLabel(self.frameOutputSetting)
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.eload_temp_lb.setFont(font)
        self.eload_temp_lb.setObjectName("eload_temp_lb")
        self.gridLayout_data.addWidget(self.eload_temp_lb, 5, 1, 1, 1)
        self.label_31 = QtWidgets.QLabel(self.frameOutputSetting)
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.label_31.setFont(font)
        self.label_31.setAlignment(QtCore.Qt.AlignCenter)
        self.label_31.setObjectName("label_31")
        self.gridLayout_data.addWidget(self.label_31, 5, 3, 1, 1)
        self.label_8 = QtWidgets.QLabel(self.frameOutputSetting)
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.label_8.setFont(font)
        self.label_8.setAlignment(QtCore.Qt.AlignCenter)
        self.label_8.setObjectName("label_8")
        self.gridLayout_data.addWidget(self.label_8, 2, 3, 1, 1)
        self.freq_lb = QtWidgets.QLabel(self.frameOutputSetting)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.freq_lb.sizePolicy().hasHeightForWidth())
        self.freq_lb.setSizePolicy(sizePolicy)
        self.freq_lb.setMinimumSize(QtCore.QSize(0, 0))
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.freq_lb.setFont(font)
        self.freq_lb.setObjectName("freq_lb")
        self.gridLayout_data.addWidget(self.freq_lb, 4, 4, 1, 1)
        self.test_protocol_lb = QtWidgets.QLabel(self.frameOutputSetting)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.test_protocol_lb.sizePolicy().hasHeightForWidth())
        self.test_protocol_lb.setSizePolicy(sizePolicy)
        self.test_protocol_lb.setMinimumSize(QtCore.QSize(0, 0))
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.test_protocol_lb.setFont(font)
        self.test_protocol_lb.setObjectName("test_protocol_lb")
        self.gridLayout_data.addWidget(self.test_protocol_lb, 0, 1, 1, 1)
        self.core_temp_lb = QtWidgets.QLabel(self.frameOutputSetting)
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.core_temp_lb.setFont(font)
        self.core_temp_lb.setObjectName("core_temp_lb")
        self.gridLayout_data.addWidget(self.core_temp_lb, 4, 1, 1, 1)
        self.nego_power_lb = QtWidgets.QLabel(self.frameOutputSetting)
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.nego_power_lb.setFont(font)
        self.nego_power_lb.setObjectName("nego_power_lb")
        self.gridLayout_data.addWidget(self.nego_power_lb, 1, 1, 1, 1)
        self.label_7 = QtWidgets.QLabel(self.frameOutputSetting)
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.label_7.setFont(font)
        self.label_7.setAlignment(QtCore.Qt.AlignCenter)
        self.label_7.setObjectName("label_7")
        self.gridLayout_data.addWidget(self.label_7, 4, 3, 1, 1)
        self.label_17 = QtWidgets.QLabel(self.frameOutputSetting)
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.label_17.setFont(font)
        self.label_17.setAlignment(QtCore.Qt.AlignCenter)
        self.label_17.setObjectName("label_17")
        self.gridLayout_data.addWidget(self.label_17, 1, 3, 1, 1)
        self.line_4 = QtWidgets.QFrame(self.frameOutputSetting)
        self.line_4.setFrameShape(QtWidgets.QFrame.VLine)
        self.line_4.setFrameShadow(QtWidgets.QFrame.Sunken)
        self.line_4.setObjectName("line_4")
        self.gridLayout_data.addWidget(self.line_4, 0, 2, 1, 1)
        self.line_9 = QtWidgets.QFrame(self.frameOutputSetting)
        self.line_9.setFrameShape(QtWidgets.QFrame.VLine)
        self.line_9.setFrameShadow(QtWidgets.QFrame.Sunken)
        self.line_9.setObjectName("line_9")
        self.gridLayout_data.addWidget(self.line_9, 6, 2, 1, 1)
        self.bridge_temp_lb = QtWidgets.QLabel(self.frameOutputSetting)
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.bridge_temp_lb.setFont(font)
        self.bridge_temp_lb.setObjectName("bridge_temp_lb")
        self.gridLayout_data.addWidget(self.bridge_temp_lb, 5, 4, 1, 1)
        self.label_11 = QtWidgets.QLabel(self.frameOutputSetting)
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei")
        font.setBold(True)
        font.setWeight(75)
        self.label_11.setFont(font)
        self.label_11.setAlignment(QtCore.Qt.AlignCenter)
        self.label_11.setObjectName("label_11")
        self.gridLayout_data.addWidget(self.label_11, 0, 0, 1, 1)
        self.label_19 = QtWidgets.QLabel(self.frameOutputSetting)
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.label_19.setFont(font)
        self.label_19.setAlignment(QtCore.Qt.AlignCenter)
        self.label_19.setObjectName("label_19")
        self.gridLayout_data.addWidget(self.label_19, 2, 0, 1, 1)
        self.line_5 = QtWidgets.QFrame(self.frameOutputSetting)
        self.line_5.setFrameShape(QtWidgets.QFrame.VLine)
        self.line_5.setFrameShadow(QtWidgets.QFrame.Sunken)
        self.line_5.setObjectName("line_5")
        self.gridLayout_data.addWidget(self.line_5, 1, 2, 1, 1)
        self.line_11 = QtWidgets.QFrame(self.frameOutputSetting)
        self.line_11.setFrameShape(QtWidgets.QFrame.VLine)
        self.line_11.setFrameShadow(QtWidgets.QFrame.Sunken)
        self.line_11.setObjectName("line_11")
        self.gridLayout_data.addWidget(self.line_11, 2, 2, 1, 1)
        self.verticalLayout_6.addLayout(self.gridLayout_data)
        self.line_3 = QtWidgets.QFrame(self.frameOutputSetting)
        self.line_3.setFrameShadow(QtWidgets.QFrame.Plain)
        self.line_3.setFrameShape(QtWidgets.QFrame.HLine)
        self.line_3.setObjectName("line_3")
        self.verticalLayout_6.addWidget(self.line_3)
        self.frame = QtWidgets.QFrame(self.frameOutputSetting)
        self.frame.setFrameShape(QtWidgets.QFrame.Box)
        self.frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.frame.setObjectName("frame")
        self.verticalLayout_27 = QtWidgets.QVBoxLayout(self.frame)
        self.verticalLayout_27.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_27.setSpacing(0)
        self.verticalLayout_27.setObjectName("verticalLayout_27")
        self.tabWidget = QtWidgets.QTabWidget(self.frame)
        self.tabWidget.setEnabled(True)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.tabWidget.sizePolicy().hasHeightForWidth())
        self.tabWidget.setSizePolicy(sizePolicy)
        self.tabWidget.setMinimumSize(QtCore.QSize(0, 0))
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei")
        self.tabWidget.setFont(font)
        self.tabWidget.setStyleSheet("QTabWidget::pane {\n"
"    border-radius: 6px;\n"
"    margin-top: -1px;\n"
"}\n"
"\n"
"QTabWidget::tab-bar {\n"
"    alignment: center;\n"
"}\n"
"\n"
"QTabBar::tab {\n"
"    border-top-left-radius: 6px;\n"
"    border-top-right-radius: 6px;\n"
"    min-width: 30px;\n"
"    min-height: 20px;\n"
"    padding: 4px 8px;\n"
"    margin-right: 2px;\n"
"}")
        self.tabWidget.setTabPosition(QtWidgets.QTabWidget.North)
        self.tabWidget.setTabShape(QtWidgets.QTabWidget.Rounded)
        self.tabWidget.setIconSize(QtCore.QSize(5, 5))
        self.tabWidget.setElideMode(QtCore.Qt.ElideMiddle)
        self.tabWidget.setUsesScrollButtons(False)
        self.tabWidget.setDocumentMode(True)
        self.tabWidget.setTabsClosable(False)
        self.tabWidget.setMovable(False)
        self.tabWidget.setTabBarAutoHide(False)
        self.tabWidget.setObjectName("tabWidget")
        self.tabPower = QtWidgets.QWidget()
        self.tabPower.setObjectName("tabPower")
        self.verticalLayout_10 = QtWidgets.QVBoxLayout(self.tabPower)
        self.verticalLayout_10.setObjectName("verticalLayout_10")
        self.groupBoxChargeConfig_4 = QtWidgets.QGroupBox(self.tabPower)
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei")
        font.setBold(True)
        font.setWeight(75)
        self.groupBoxChargeConfig_4.setFont(font)
        self.groupBoxChargeConfig_4.setObjectName("groupBoxChargeConfig_4")
        self.verticalLayout_22 = QtWidgets.QVBoxLayout(self.groupBoxChargeConfig_4)
        self.verticalLayout_22.setObjectName("verticalLayout_22")
        self.horizontalWidget = QtWidgets.QWidget(self.groupBoxChargeConfig_4)
        self.horizontalWidget.setObjectName("horizontalWidget")
        self.horizontalLayout_16 = QtWidgets.QHBoxLayout(self.horizontalWidget)
        self.horizontalLayout_16.setObjectName("horizontalLayout_16")
        self.set_protocol_lb = QtWidgets.QLabel(self.horizontalWidget)
        self.set_protocol_lb.setObjectName("set_protocol_lb")
        self.horizontalLayout_16.addWidget(self.set_protocol_lb)
        self.set_protocol_cb = QtWidgets.QComboBox(self.horizontalWidget)
        self.set_protocol_cb.setObjectName("set_protocol_cb")
        self.set_protocol_cb.addItem("")
        self.set_protocol_cb.addItem("")
        self.set_protocol_cb.addItem("")
        self.set_protocol_cb.addItem("")
        self.set_protocol_cb.addItem("")
        self.set_protocol_cb.addItem("")
        self.set_protocol_cb.addItem("")
        self.set_protocol_cb.addItem("")
        self.set_protocol_cb.addItem("")
        self.set_protocol_cb.addItem("")
        self.set_protocol_cb.addItem("")
        self.set_protocol_cb.addItem("")
        self.set_protocol_cb.addItem("")
        self.set_protocol_cb.addItem("")
        self.set_protocol_cb.addItem("")
        self.set_protocol_cb.addItem("")
        self.set_protocol_cb.addItem("")
        self.set_protocol_cb.addItem("")
        self.set_protocol_cb.addItem("")
        self.horizontalLayout_16.addWidget(self.set_protocol_cb)
        self.set_protocol_bt = QtWidgets.QPushButton(self.horizontalWidget)
        self.set_protocol_bt.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.set_protocol_bt.setObjectName("set_protocol_bt")
        self.horizontalLayout_16.addWidget(self.set_protocol_bt)
        self.verticalLayout_22.addWidget(self.horizontalWidget)
        self.line = QtWidgets.QFrame(self.groupBoxChargeConfig_4)
        self.line.setFrameShape(QtWidgets.QFrame.HLine)
        self.line.setFrameShadow(QtWidgets.QFrame.Sunken)
        self.line.setObjectName("line")
        self.verticalLayout_22.addWidget(self.line)
        self.horizontalWidget1 = QtWidgets.QWidget(self.groupBoxChargeConfig_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Minimum)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(20)
        sizePolicy.setHeightForWidth(self.horizontalWidget1.sizePolicy().hasHeightForWidth())
        self.horizontalWidget1.setSizePolicy(sizePolicy)
        self.horizontalWidget1.setObjectName("horizontalWidget1")
        self.horizontalLayout_17 = QtWidgets.QHBoxLayout(self.horizontalWidget1)
        self.horizontalLayout_17.setObjectName("horizontalLayout_17")
        self.label_10 = QtWidgets.QLabel(self.horizontalWidget1)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Minimum)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_10.sizePolicy().hasHeightForWidth())
        self.label_10.setSizePolicy(sizePolicy)
        self.label_10.setObjectName("label_10")
        self.horizontalLayout_17.addWidget(self.label_10)
        self.fod_offset_lb = QtWidgets.QLabel(self.horizontalWidget1)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Minimum)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.fod_offset_lb.sizePolicy().hasHeightForWidth())
        self.fod_offset_lb.setSizePolicy(sizePolicy)
        self.fod_offset_lb.setAlignment(QtCore.Qt.AlignCenter)
        self.fod_offset_lb.setObjectName("fod_offset_lb")
        self.horizontalLayout_17.addWidget(self.fod_offset_lb)
        self.set_fod_offset_bt = QtWidgets.QPushButton(self.horizontalWidget1)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Minimum)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.set_fod_offset_bt.sizePolicy().hasHeightForWidth())
        self.set_fod_offset_bt.setSizePolicy(sizePolicy)
        self.set_fod_offset_bt.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.set_fod_offset_bt.setFocusPolicy(QtCore.Qt.StrongFocus)
        self.set_fod_offset_bt.setObjectName("set_fod_offset_bt")
        self.horizontalLayout_17.addWidget(self.set_fod_offset_bt)
        self.verticalLayout_22.addWidget(self.horizontalWidget1)
        self.set_fod_offset_Slider = QtWidgets.QSlider(self.groupBoxChargeConfig_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.set_fod_offset_Slider.sizePolicy().hasHeightForWidth())
        self.set_fod_offset_Slider.setSizePolicy(sizePolicy)
        self.set_fod_offset_Slider.setCursor(QtGui.QCursor(QtCore.Qt.OpenHandCursor))
        self.set_fod_offset_Slider.setAcceptDrops(False)
        self.set_fod_offset_Slider.setAutoFillBackground(False)
        self.set_fod_offset_Slider.setMinimum(-2000)
        self.set_fod_offset_Slider.setMaximum(2000)
        self.set_fod_offset_Slider.setProperty("value", 500)
        self.set_fod_offset_Slider.setOrientation(QtCore.Qt.Horizontal)
        self.set_fod_offset_Slider.setInvertedAppearance(False)
        self.set_fod_offset_Slider.setInvertedControls(False)
        self.set_fod_offset_Slider.setObjectName("set_fod_offset_Slider")
        self.verticalLayout_22.addWidget(self.set_fod_offset_Slider)
        self.line_13 = QtWidgets.QFrame(self.groupBoxChargeConfig_4)
        self.line_13.setFrameShape(QtWidgets.QFrame.HLine)
        self.line_13.setFrameShadow(QtWidgets.QFrame.Sunken)
        self.line_13.setObjectName("line_13")
        self.verticalLayout_22.addWidget(self.line_13)
        self.horizontalLayout_24 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_24.setObjectName("horizontalLayout_24")
        self.label_64 = QtWidgets.QLabel(self.groupBoxChargeConfig_4)
        font = QtGui.QFont()
        font.setPointSize(10)
        self.label_64.setFont(font)
        self.label_64.setObjectName("label_64")
        self.horizontalLayout_24.addWidget(self.label_64)
        self.verticalLayout_22.addLayout(self.horizontalLayout_24)
        self.horizontalLayout_19 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_19.setObjectName("horizontalLayout_19")
        self.set_volta_ld = QtWidgets.QLineEdit(self.groupBoxChargeConfig_4)
        self.set_volta_ld.setObjectName("set_volta_ld")
        self.horizontalLayout_19.addWidget(self.set_volta_ld)
        self.label_66 = QtWidgets.QLabel(self.groupBoxChargeConfig_4)
        self.label_66.setObjectName("label_66")
        self.horizontalLayout_19.addWidget(self.label_66)
        self.set_current_ld = QtWidgets.QLineEdit(self.groupBoxChargeConfig_4)
        self.set_current_ld.setObjectName("set_current_ld")
        self.horizontalLayout_19.addWidget(self.set_current_ld)
        self.label_67 = QtWidgets.QLabel(self.groupBoxChargeConfig_4)
        self.label_67.setObjectName("label_67")
        self.horizontalLayout_19.addWidget(self.label_67)
        self.set_power_data_bt = QtWidgets.QPushButton(self.groupBoxChargeConfig_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(10)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.set_power_data_bt.sizePolicy().hasHeightForWidth())
        self.set_power_data_bt.setSizePolicy(sizePolicy)
        self.set_power_data_bt.setObjectName("set_power_data_bt")
        self.horizontalLayout_19.addWidget(self.set_power_data_bt)
        self.verticalLayout_22.addLayout(self.horizontalLayout_19)
        self.verticalLayout_10.addWidget(self.groupBoxChargeConfig_4)
        self.send_bat_full_bt = QtWidgets.QPushButton(self.tabPower)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.send_bat_full_bt.sizePolicy().hasHeightForWidth())
        self.send_bat_full_bt.setSizePolicy(sizePolicy)
        self.send_bat_full_bt.setMinimumSize(QtCore.QSize(0, 25))
        self.send_bat_full_bt.setObjectName("send_bat_full_bt")
        self.verticalLayout_10.addWidget(self.send_bat_full_bt)
        self.groupBoxChargeControl_3 = QtWidgets.QGroupBox(self.tabPower)
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei")
        font.setBold(True)
        font.setWeight(75)
        self.groupBoxChargeControl_3.setFont(font)
        self.groupBoxChargeControl_3.setObjectName("groupBoxChargeControl_3")
        self.verticalLayout_24 = QtWidgets.QVBoxLayout(self.groupBoxChargeControl_3)
        self.verticalLayout_24.setObjectName("verticalLayout_24")
        self.horizontalLayout_22 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_22.setObjectName("horizontalLayout_22")
        self.send_ce_lb = QtWidgets.QLabel(self.groupBoxChargeControl_3)
        self.send_ce_lb.setMinimumSize(QtCore.QSize(0, 25))
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei")
        font.setBold(True)
        font.setWeight(75)
        self.send_ce_lb.setFont(font)
        self.send_ce_lb.setAlignment(QtCore.Qt.AlignCenter)
        self.send_ce_lb.setObjectName("send_ce_lb")
        self.horizontalLayout_22.addWidget(self.send_ce_lb)
        self.send_ce_sp = QtWidgets.QSpinBox(self.groupBoxChargeControl_3)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.send_ce_sp.sizePolicy().hasHeightForWidth())
        self.send_ce_sp.setSizePolicy(sizePolicy)
        self.send_ce_sp.setMinimumSize(QtCore.QSize(0, 0))
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei")
        font.setBold(True)
        font.setWeight(75)
        self.send_ce_sp.setFont(font)
        self.send_ce_sp.setAlignment(QtCore.Qt.AlignCenter)
        self.send_ce_sp.setSuffix("")
        self.send_ce_sp.setMinimum(-128)
        self.send_ce_sp.setMaximum(128)
        self.send_ce_sp.setObjectName("send_ce_sp")
        self.horizontalLayout_22.addWidget(self.send_ce_sp)
        self.send_ce_bt = QtWidgets.QPushButton(self.groupBoxChargeControl_3)
        self.send_ce_bt.setObjectName("send_ce_bt")
        self.horizontalLayout_22.addWidget(self.send_ce_bt)
        self.verticalLayout_24.addLayout(self.horizontalLayout_22)
        self.line_2 = QtWidgets.QFrame(self.groupBoxChargeControl_3)
        self.line_2.setFrameShape(QtWidgets.QFrame.HLine)
        self.line_2.setFrameShadow(QtWidgets.QFrame.Sunken)
        self.line_2.setObjectName("line_2")
        self.verticalLayout_24.addWidget(self.line_2)
        self.horizontalLayout_23 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_23.setObjectName("horizontalLayout_23")
        self.send_ept_lb = QtWidgets.QLabel(self.groupBoxChargeControl_3)
        self.send_ept_lb.setMinimumSize(QtCore.QSize(0, 25))
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei")
        font.setBold(True)
        font.setWeight(75)
        self.send_ept_lb.setFont(font)
        self.send_ept_lb.setAlignment(QtCore.Qt.AlignCenter)
        self.send_ept_lb.setObjectName("send_ept_lb")
        self.horizontalLayout_23.addWidget(self.send_ept_lb)
        self.send_ept_cb = QtWidgets.QComboBox(self.groupBoxChargeControl_3)
        self.send_ept_cb.setObjectName("send_ept_cb")
        self.send_ept_cb.addItem("")
        self.send_ept_cb.addItem("")
        self.send_ept_cb.addItem("")
        self.send_ept_cb.addItem("")
        self.send_ept_cb.addItem("")
        self.send_ept_cb.addItem("")
        self.send_ept_cb.addItem("")
        self.send_ept_cb.addItem("")
        self.send_ept_cb.addItem("")
        self.send_ept_cb.addItem("")
        self.send_ept_cb.addItem("")
        self.send_ept_cb.addItem("")
        self.send_ept_cb.addItem("")
        self.horizontalLayout_23.addWidget(self.send_ept_cb)
        self.send_ept_bt = QtWidgets.QPushButton(self.groupBoxChargeControl_3)
        self.send_ept_bt.setObjectName("send_ept_bt")
        self.horizontalLayout_23.addWidget(self.send_ept_bt)
        self.verticalLayout_24.addLayout(self.horizontalLayout_23)
        self.line_14 = QtWidgets.QFrame(self.groupBoxChargeControl_3)
        self.line_14.setFrameShape(QtWidgets.QFrame.HLine)
        self.line_14.setFrameShadow(QtWidgets.QFrame.Sunken)
        self.line_14.setObjectName("line_14")
        self.verticalLayout_24.addWidget(self.line_14)
        self.label_65 = QtWidgets.QLabel(self.groupBoxChargeControl_3)
        font = QtGui.QFont()
        font.setPointSize(8)
        self.label_65.setFont(font)
        self.label_65.setObjectName("label_65")
        self.verticalLayout_24.addWidget(self.label_65)
        self.horizontalLayout_20 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_20.setObjectName("horizontalLayout_20")
        self.protocol_data_ld = QtWidgets.QLineEdit(self.groupBoxChargeControl_3)
        self.protocol_data_ld.setInputMask("")
        self.protocol_data_ld.setMaxLength(32767)
        self.protocol_data_ld.setObjectName("protocol_data_ld")
        self.horizontalLayout_20.addWidget(self.protocol_data_ld)
        self.protocol_send_data_bt = QtWidgets.QPushButton(self.groupBoxChargeControl_3)
        self.protocol_send_data_bt.setObjectName("protocol_send_data_bt")
        self.horizontalLayout_20.addWidget(self.protocol_send_data_bt)
        self.verticalLayout_24.addLayout(self.horizontalLayout_20)
        self.verticalLayout_10.addWidget(self.groupBoxChargeControl_3)
        self.start_auto_test_bt = QtWidgets.QPushButton(self.tabPower)
        self.start_auto_test_bt.setObjectName("start_auto_test_bt")
        self.verticalLayout_10.addWidget(self.start_auto_test_bt)
        spacerItem3 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_10.addItem(spacerItem3)
        self.tabWidget.addTab(self.tabPower, "")
        self.tabWaveGen = QtWidgets.QWidget()
        self.tabWaveGen.setObjectName("tabWaveGen")
        self.verticalLayout_5 = QtWidgets.QVBoxLayout(self.tabWaveGen)
        self.verticalLayout_5.setObjectName("verticalLayout_5")
        self.groupBox_2 = QtWidgets.QGroupBox(self.tabWaveGen)
        font = QtGui.QFont()
        font.setPointSize(10)
        self.groupBox_2.setFont(font)
        self.groupBox_2.setObjectName("groupBox_2")
        self.verticalLayout_17 = QtWidgets.QVBoxLayout(self.groupBox_2)
        self.verticalLayout_17.setObjectName("verticalLayout_17")
        self.gridLayout_data_2 = QtWidgets.QGridLayout()
        self.gridLayout_data_2.setContentsMargins(9, 9, 9, 9)
        self.gridLayout_data_2.setObjectName("gridLayout_data_2")
        self.support_dup_lb = QtWidgets.QLabel(self.groupBox_2)
        self.support_dup_lb.setObjectName("support_dup_lb")
        self.gridLayout_data_2.addWidget(self.support_dup_lb, 3, 1, 1, 1)
        self.support_ob_lb = QtWidgets.QLabel(self.groupBox_2)
        self.support_ob_lb.setObjectName("support_ob_lb")
        self.gridLayout_data_2.addWidget(self.support_ob_lb, 4, 1, 1, 1)
        self.manufacturer_id_lb = QtWidgets.QLabel(self.groupBox_2)
        self.manufacturer_id_lb.setObjectName("manufacturer_id_lb")
        self.gridLayout_data_2.addWidget(self.manufacturer_id_lb, 1, 1, 1, 1)
        self.label_22 = QtWidgets.QLabel(self.groupBox_2)
        self.label_22.setAlignment(QtCore.Qt.AlignCenter)
        self.label_22.setObjectName("label_22")
        self.gridLayout_data_2.addWidget(self.label_22, 1, 0, 1, 1)
        self.Coil_temp_bt_2 = QtWidgets.QLabel(self.groupBox_2)
        self.Coil_temp_bt_2.setAlignment(QtCore.Qt.AlignCenter)
        self.Coil_temp_bt_2.setObjectName("Coil_temp_bt_2")
        self.gridLayout_data_2.addWidget(self.Coil_temp_bt_2, 3, 0, 1, 1)
        self.label_27 = QtWidgets.QLabel(self.groupBox_2)
        self.label_27.setAlignment(QtCore.Qt.AlignCenter)
        self.label_27.setObjectName("label_27")
        self.gridLayout_data_2.addWidget(self.label_27, 6, 0, 1, 1)
        self.wpid_value_lb = QtWidgets.QLabel(self.groupBox_2)
        self.wpid_value_lb.setObjectName("wpid_value_lb")
        self.gridLayout_data_2.addWidget(self.wpid_value_lb, 5, 1, 1, 1)
        self.support_ar_lb = QtWidgets.QLabel(self.groupBox_2)
        self.support_ar_lb.setObjectName("support_ar_lb")
        self.gridLayout_data_2.addWidget(self.support_ar_lb, 2, 1, 1, 1)
        self.label_34 = QtWidgets.QLabel(self.groupBox_2)
        self.label_34.setAlignment(QtCore.Qt.AlignCenter)
        self.label_34.setObjectName("label_34")
        self.gridLayout_data_2.addWidget(self.label_34, 4, 0, 1, 1)
        self.label_25 = QtWidgets.QLabel(self.groupBox_2)
        self.label_25.setAlignment(QtCore.Qt.AlignCenter)
        self.label_25.setObjectName("label_25")
        self.gridLayout_data_2.addWidget(self.label_25, 5, 0, 1, 1)
        self.label_28 = QtWidgets.QLabel(self.groupBox_2)
        self.label_28.setAlignment(QtCore.Qt.AlignCenter)
        self.label_28.setObjectName("label_28")
        self.gridLayout_data_2.addWidget(self.label_28, 2, 0, 1, 1)
        self.nrs_value_lb = QtWidgets.QLabel(self.groupBox_2)
        self.nrs_value_lb.setObjectName("nrs_value_lb")
        self.gridLayout_data_2.addWidget(self.nrs_value_lb, 6, 1, 1, 1)
        self.label_20 = QtWidgets.QLabel(self.groupBox_2)
        self.label_20.setAlignment(QtCore.Qt.AlignCenter)
        self.label_20.setObjectName("label_20")
        self.gridLayout_data_2.addWidget(self.label_20, 0, 0, 1, 1)
        self.qi_version_lb = QtWidgets.QLabel(self.groupBox_2)
        self.qi_version_lb.setObjectName("qi_version_lb")
        self.gridLayout_data_2.addWidget(self.qi_version_lb, 0, 1, 1, 1)
        self.verticalLayout_17.addLayout(self.gridLayout_data_2)
        self.verticalLayout_5.addWidget(self.groupBox_2)
        self.groupBox_3 = QtWidgets.QGroupBox(self.tabWaveGen)
        font = QtGui.QFont()
        font.setPointSize(10)
        self.groupBox_3.setFont(font)
        self.groupBox_3.setObjectName("groupBox_3")
        self.verticalLayout_20 = QtWidgets.QVBoxLayout(self.groupBox_3)
        self.verticalLayout_20.setObjectName("verticalLayout_20")
        self.gridLayout_data_3 = QtWidgets.QGridLayout()
        self.gridLayout_data_3.setContentsMargins(9, 9, 9, 9)
        self.gridLayout_data_3.setObjectName("gridLayout_data_3")
        self.app_value_lb = QtWidgets.QLabel(self.groupBox_3)
        self.app_value_lb.setObjectName("app_value_lb")
        self.gridLayout_data_3.addWidget(self.app_value_lb, 6, 1, 1, 1)
        self.support_calibration_lb = QtWidgets.QLabel(self.groupBox_3)
        self.support_calibration_lb.setObjectName("support_calibration_lb")
        self.gridLayout_data_3.addWidget(self.support_calibration_lb, 2, 1, 1, 1)
        self.buffer_size_lb = QtWidgets.QLabel(self.groupBox_3)
        self.buffer_size_lb.setObjectName("buffer_size_lb")
        self.gridLayout_data_3.addWidget(self.buffer_size_lb, 3, 1, 1, 1)
        self.label_51 = QtWidgets.QLabel(self.groupBox_3)
        self.label_51.setMinimumSize(QtCore.QSize(0, 0))
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei")
        font.setPointSize(10)
        self.label_51.setFont(font)
        self.label_51.setFocusPolicy(QtCore.Qt.ClickFocus)
        self.label_51.setAlignment(QtCore.Qt.AlignCenter)
        self.label_51.setObjectName("label_51")
        self.gridLayout_data_3.addWidget(self.label_51, 0, 0, 1, 1)
        self.uid_value_lb = QtWidgets.QLabel(self.groupBox_3)
        self.uid_value_lb.setObjectName("uid_value_lb")
        self.gridLayout_data_3.addWidget(self.uid_value_lb, 7, 1, 1, 1)
        self.label_49 = QtWidgets.QLabel(self.groupBox_3)
        self.label_49.setAlignment(QtCore.Qt.AlignCenter)
        self.label_49.setObjectName("label_49")
        self.gridLayout_data_3.addWidget(self.label_49, 4, 0, 1, 1)
        self.power_limit_reason_lb = QtWidgets.QLabel(self.groupBox_3)
        self.power_limit_reason_lb.setObjectName("power_limit_reason_lb")
        self.gridLayout_data_3.addWidget(self.power_limit_reason_lb, 1, 1, 1, 1)
        self.device_id_lb = QtWidgets.QLabel(self.groupBox_3)
        self.device_id_lb.setObjectName("device_id_lb")
        self.gridLayout_data_3.addWidget(self.device_id_lb, 0, 1, 1, 1)
        self.Coil_temp_bt_3 = QtWidgets.QLabel(self.groupBox_3)
        self.Coil_temp_bt_3.setAlignment(QtCore.Qt.AlignCenter)
        self.Coil_temp_bt_3.setObjectName("Coil_temp_bt_3")
        self.gridLayout_data_3.addWidget(self.Coil_temp_bt_3, 6, 0, 1, 1)
        self.concurrent_streams_lb = QtWidgets.QLabel(self.groupBox_3)
        self.concurrent_streams_lb.setObjectName("concurrent_streams_lb")
        self.gridLayout_data_3.addWidget(self.concurrent_streams_lb, 4, 1, 1, 1)
        self.g_coil_rx_lb = QtWidgets.QLabel(self.groupBox_3)
        self.g_coil_rx_lb.setObjectName("g_coil_rx_lb")
        self.gridLayout_data_3.addWidget(self.g_coil_rx_lb, 5, 1, 1, 1)
        self.label_46 = QtWidgets.QLabel(self.groupBox_3)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_46.sizePolicy().hasHeightForWidth())
        self.label_46.setSizePolicy(sizePolicy)
        self.label_46.setMinimumSize(QtCore.QSize(0, 0))
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei")
        font.setPointSize(9)
        self.label_46.setFont(font)
        self.label_46.setFocusPolicy(QtCore.Qt.ClickFocus)
        self.label_46.setAcceptDrops(False)
        self.label_46.setLayoutDirection(QtCore.Qt.RightToLeft)
        self.label_46.setAlignment(QtCore.Qt.AlignCenter)
        self.label_46.setObjectName("label_46")
        self.gridLayout_data_3.addWidget(self.label_46, 2, 0, 1, 1)
        self.label_54 = QtWidgets.QLabel(self.groupBox_3)
        self.label_54.setAlignment(QtCore.Qt.AlignCenter)
        self.label_54.setObjectName("label_54")
        self.gridLayout_data_3.addWidget(self.label_54, 1, 0, 1, 1)
        self.label_55 = QtWidgets.QLabel(self.groupBox_3)
        self.label_55.setAlignment(QtCore.Qt.AlignCenter)
        self.label_55.setObjectName("label_55")
        self.gridLayout_data_3.addWidget(self.label_55, 5, 0, 1, 1)
        self.label_56 = QtWidgets.QLabel(self.groupBox_3)
        self.label_56.setAlignment(QtCore.Qt.AlignCenter)
        self.label_56.setObjectName("label_56")
        self.gridLayout_data_3.addWidget(self.label_56, 7, 0, 1, 1)
        self.label_47 = QtWidgets.QLabel(self.groupBox_3)
        self.label_47.setAlignment(QtCore.Qt.AlignCenter)
        self.label_47.setObjectName("label_47")
        self.gridLayout_data_3.addWidget(self.label_47, 3, 0, 1, 1)
        self.verticalLayout_20.addLayout(self.gridLayout_data_3)
        self.verticalLayout_5.addWidget(self.groupBox_3)
        spacerItem4 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_5.addItem(spacerItem4)
        self.tabWidget.addTab(self.tabWaveGen, "")
        self.tabBat = QtWidgets.QWidget()
        self.tabBat.setObjectName("tabBat")
        self.verticalLayout_26 = QtWidgets.QVBoxLayout(self.tabBat)
        self.verticalLayout_26.setContentsMargins(5, 5, 5, 5)
        self.verticalLayout_26.setSpacing(4)
        self.verticalLayout_26.setObjectName("verticalLayout_26")
        self.btnBatSim = QtWidgets.QPushButton(self.tabBat)
        self.btnBatSim.setObjectName("btnBatSim")
        self.verticalLayout_26.addWidget(self.btnBatSim)
        self.horizontalLayout_47 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_47.setObjectName("horizontalLayout_47")
        self.labelBatSimTime = QtWidgets.QLabel(self.tabBat)
        self.labelBatSimTime.setMinimumSize(QtCore.QSize(0, 25))
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei")
        font.setBold(True)
        font.setWeight(75)
        self.labelBatSimTime.setFont(font)
        self.labelBatSimTime.setAlignment(QtCore.Qt.AlignCenter)
        self.labelBatSimTime.setObjectName("labelBatSimTime")
        self.horizontalLayout_47.addWidget(self.labelBatSimTime)
        self.horizontalLayout_47.setStretch(0, 2)
        self.verticalLayout_26.addLayout(self.horizontalLayout_47)
        self.scrollAreaBatSim = QtWidgets.QScrollArea(self.tabBat)
        self.scrollAreaBatSim.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.scrollAreaBatSim.setWidgetResizable(True)
        self.scrollAreaBatSim.setObjectName("scrollAreaBatSim")
        self.scrollAreaWidgetContents_5 = QtWidgets.QWidget()
        self.scrollAreaWidgetContents_5.setGeometry(QtCore.QRect(0, 0, 358, 448))
        self.scrollAreaWidgetContents_5.setObjectName("scrollAreaWidgetContents_5")
        self.verticalLayout_25 = QtWidgets.QVBoxLayout(self.scrollAreaWidgetContents_5)
        self.verticalLayout_25.setSpacing(8)
        self.verticalLayout_25.setObjectName("verticalLayout_25")
        self.groupBoxChargeConfig = QtWidgets.QGroupBox(self.scrollAreaWidgetContents_5)
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei")
        font.setBold(True)
        font.setWeight(75)
        self.groupBoxChargeConfig.setFont(font)
        self.groupBoxChargeConfig.setObjectName("groupBoxChargeConfig")
        self.verticalLayoutChargeConfig = QtWidgets.QVBoxLayout(self.groupBoxChargeConfig)
        self.verticalLayoutChargeConfig.setSpacing(6)
        self.verticalLayoutChargeConfig.setObjectName("verticalLayoutChargeConfig")
        self.horizontalLayout_48 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_48.setObjectName("horizontalLayout_48")
        self.btnBatSimLoad = QtWidgets.QPushButton(self.groupBoxChargeConfig)
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei")
        font.setBold(True)
        font.setWeight(75)
        self.btnBatSimLoad.setFont(font)
        self.btnBatSimLoad.setObjectName("btnBatSimLoad")
        self.horizontalLayout_48.addWidget(self.btnBatSimLoad)
        self.btnBatSimPreview = QtWidgets.QPushButton(self.groupBoxChargeConfig)
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei")
        font.setBold(True)
        font.setWeight(75)
        self.btnBatSimPreview.setFont(font)
        self.btnBatSimPreview.setObjectName("btnBatSimPreview")
        self.horizontalLayout_48.addWidget(self.btnBatSimPreview)
        self.horizontalLayout_48.setStretch(0, 1)
        self.horizontalLayout_48.setStretch(1, 1)
        self.verticalLayoutChargeConfig.addLayout(self.horizontalLayout_48)
        self.horizontalLayout_42 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_42.setObjectName("horizontalLayout_42")
        self.label_32 = QtWidgets.QLabel(self.groupBoxChargeConfig)
        self.label_32.setMinimumSize(QtCore.QSize(0, 20))
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei")
        font.setBold(True)
        font.setWeight(75)
        self.label_32.setFont(font)
        self.label_32.setAlignment(QtCore.Qt.AlignCenter)
        self.label_32.setObjectName("label_32")
        self.horizontalLayout_42.addWidget(self.label_32)
        self.comboBatSimCurve = QtWidgets.QComboBox(self.groupBoxChargeConfig)
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei")
        font.setBold(True)
        font.setWeight(75)
        self.comboBatSimCurve.setFont(font)
        self.comboBatSimCurve.setObjectName("comboBatSimCurve")
        self.horizontalLayout_42.addWidget(self.comboBatSimCurve)
        self.horizontalLayout_42.setStretch(0, 2)
        self.horizontalLayout_42.setStretch(1, 3)
        self.verticalLayoutChargeConfig.addLayout(self.horizontalLayout_42)
        self.verticalLayout_25.addWidget(self.groupBoxChargeConfig)
        self.groupBoxChargeControl = QtWidgets.QGroupBox(self.scrollAreaWidgetContents_5)
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei")
        font.setBold(True)
        font.setWeight(75)
        self.groupBoxChargeControl.setFont(font)
        self.groupBoxChargeControl.setObjectName("groupBoxChargeControl")
        self.gridLayoutChargeControl = QtWidgets.QGridLayout(self.groupBoxChargeControl)
        self.gridLayoutChargeControl.setSpacing(8)
        self.gridLayoutChargeControl.setObjectName("gridLayoutChargeControl")
        self.label_37 = QtWidgets.QLabel(self.groupBoxChargeControl)
        self.label_37.setMinimumSize(QtCore.QSize(0, 25))
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei")
        font.setBold(True)
        font.setWeight(75)
        self.label_37.setFont(font)
        self.label_37.setAlignment(QtCore.Qt.AlignCenter)
        self.label_37.setObjectName("label_37")
        self.gridLayoutChargeControl.addWidget(self.label_37, 1, 0, 1, 1)
        self.label_35 = QtWidgets.QLabel(self.groupBoxChargeControl)
        self.label_35.setMinimumSize(QtCore.QSize(0, 25))
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei")
        font.setBold(True)
        font.setWeight(75)
        self.label_35.setFont(font)
        self.label_35.setAlignment(QtCore.Qt.AlignCenter)
        self.label_35.setObjectName("label_35")
        self.gridLayoutChargeControl.addWidget(self.label_35, 0, 0, 1, 1)
        self.spinBoxBatSimCurrent = QtWidgets.QDoubleSpinBox(self.groupBoxChargeControl)
        self.spinBoxBatSimCurrent.setMinimumSize(QtCore.QSize(100, 25))
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei")
        font.setBold(True)
        font.setWeight(75)
        self.spinBoxBatSimCurrent.setFont(font)
        self.spinBoxBatSimCurrent.setAlignment(QtCore.Qt.AlignCenter)
        self.spinBoxBatSimCurrent.setDecimals(1)
        self.spinBoxBatSimCurrent.setMinimum(0.0)
        self.spinBoxBatSimCurrent.setMaximum(100.0)
        self.spinBoxBatSimCurrent.setSingleStep(1.0)
        self.spinBoxBatSimCurrent.setProperty("value", 0.0)
        self.spinBoxBatSimCurrent.setObjectName("spinBoxBatSimCurrent")
        self.gridLayoutChargeControl.addWidget(self.spinBoxBatSimCurrent, 0, 1, 1, 1)
        self.spinBoxBatSimStop = QtWidgets.QDoubleSpinBox(self.groupBoxChargeControl)
        self.spinBoxBatSimStop.setMinimumSize(QtCore.QSize(100, 25))
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei")
        font.setBold(True)
        font.setWeight(75)
        self.spinBoxBatSimStop.setFont(font)
        self.spinBoxBatSimStop.setAlignment(QtCore.Qt.AlignCenter)
        self.spinBoxBatSimStop.setDecimals(1)
        self.spinBoxBatSimStop.setMinimum(0.0)
        self.spinBoxBatSimStop.setMaximum(100.0)
        self.spinBoxBatSimStop.setSingleStep(1.0)
        self.spinBoxBatSimStop.setProperty("value", 100.0)
        self.spinBoxBatSimStop.setObjectName("spinBoxBatSimStop")
        self.gridLayoutChargeControl.addWidget(self.spinBoxBatSimStop, 1, 1, 1, 1)
        self.label_38 = QtWidgets.QLabel(self.groupBoxChargeControl)
        self.label_38.setMinimumSize(QtCore.QSize(0, 25))
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei")
        font.setBold(True)
        font.setWeight(75)
        self.label_38.setFont(font)
        self.label_38.setAlignment(QtCore.Qt.AlignCenter)
        self.label_38.setObjectName("label_38")
        self.gridLayoutChargeControl.addWidget(self.label_38, 2, 0, 1, 1)
        self.spinBoxBatSimLoopFreq = QtWidgets.QDoubleSpinBox(self.groupBoxChargeControl)
        self.spinBoxBatSimLoopFreq.setMinimumSize(QtCore.QSize(100, 25))
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei")
        font.setBold(True)
        font.setWeight(75)
        self.spinBoxBatSimLoopFreq.setFont(font)
        self.spinBoxBatSimLoopFreq.setAlignment(QtCore.Qt.AlignCenter)
        self.spinBoxBatSimLoopFreq.setDecimals(1)
        self.spinBoxBatSimLoopFreq.setMinimum(1.0)
        self.spinBoxBatSimLoopFreq.setMaximum(100.0)
        self.spinBoxBatSimLoopFreq.setSingleStep(1.0)
        self.spinBoxBatSimLoopFreq.setProperty("value", 30.0)
        self.spinBoxBatSimLoopFreq.setObjectName("spinBoxBatSimLoopFreq")
        self.gridLayoutChargeControl.addWidget(self.spinBoxBatSimLoopFreq, 2, 1, 1, 1)
        self.verticalLayout_25.addWidget(self.groupBoxChargeControl)
        self.groupBoxBatteryParams = QtWidgets.QGroupBox(self.scrollAreaWidgetContents_5)
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei")
        font.setBold(True)
        font.setWeight(75)
        self.groupBoxBatteryParams.setFont(font)
        self.groupBoxBatteryParams.setObjectName("groupBoxBatteryParams")
        self.gridLayoutBatteryParams = QtWidgets.QGridLayout(self.groupBoxBatteryParams)
        self.gridLayoutBatteryParams.setSpacing(8)
        self.gridLayoutBatteryParams.setObjectName("gridLayoutBatteryParams")
        self.label_33 = QtWidgets.QLabel(self.groupBoxBatteryParams)
        self.label_33.setMinimumSize(QtCore.QSize(0, 25))
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei")
        font.setBold(True)
        font.setWeight(75)
        self.label_33.setFont(font)
        self.label_33.setAlignment(QtCore.Qt.AlignCenter)
        self.label_33.setObjectName("label_33")
        self.gridLayoutBatteryParams.addWidget(self.label_33, 0, 0, 1, 1)
        self.spinBoxBatSimCap = QtWidgets.QDoubleSpinBox(self.groupBoxBatteryParams)
        self.spinBoxBatSimCap.setMinimumSize(QtCore.QSize(100, 25))
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei")
        font.setBold(True)
        font.setWeight(75)
        self.spinBoxBatSimCap.setFont(font)
        self.spinBoxBatSimCap.setAlignment(QtCore.Qt.AlignCenter)
        self.spinBoxBatSimCap.setDecimals(1)
        self.spinBoxBatSimCap.setMinimum(0.1)
        self.spinBoxBatSimCap.setMaximum(999.0)
        self.spinBoxBatSimCap.setSingleStep(0.5)
        self.spinBoxBatSimCap.setProperty("value", 10.0)
        self.spinBoxBatSimCap.setObjectName("spinBoxBatSimCap")
        self.gridLayoutBatteryParams.addWidget(self.spinBoxBatSimCap, 0, 1, 1, 1)
        self.label_40 = QtWidgets.QLabel(self.groupBoxBatteryParams)
        self.label_40.setMinimumSize(QtCore.QSize(0, 25))
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei")
        font.setBold(True)
        font.setWeight(75)
        self.label_40.setFont(font)
        self.label_40.setAlignment(QtCore.Qt.AlignCenter)
        self.label_40.setObjectName("label_40")
        self.gridLayoutBatteryParams.addWidget(self.label_40, 1, 0, 1, 1)
        self.spinBoxBatSimRes = QtWidgets.QDoubleSpinBox(self.groupBoxBatteryParams)
        self.spinBoxBatSimRes.setMinimumSize(QtCore.QSize(100, 25))
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei")
        font.setBold(True)
        font.setWeight(75)
        self.spinBoxBatSimRes.setFont(font)
        self.spinBoxBatSimRes.setAlignment(QtCore.Qt.AlignCenter)
        self.spinBoxBatSimRes.setDecimals(1)
        self.spinBoxBatSimRes.setMinimum(0.0)
        self.spinBoxBatSimRes.setMaximum(1000.0)
        self.spinBoxBatSimRes.setSingleStep(1.0)
        self.spinBoxBatSimRes.setProperty("value", 50.0)
        self.spinBoxBatSimRes.setObjectName("spinBoxBatSimRes")
        self.gridLayoutBatteryParams.addWidget(self.spinBoxBatSimRes, 1, 1, 1, 1)
        self.label_41 = QtWidgets.QLabel(self.groupBoxBatteryParams)
        self.label_41.setMinimumSize(QtCore.QSize(0, 25))
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei")
        font.setBold(True)
        font.setWeight(75)
        self.label_41.setFont(font)
        self.label_41.setAlignment(QtCore.Qt.AlignCenter)
        self.label_41.setObjectName("label_41")
        self.gridLayoutBatteryParams.addWidget(self.label_41, 2, 0, 1, 1)
        self.spinBoxBatSimCells = QtWidgets.QSpinBox(self.groupBoxBatteryParams)
        self.spinBoxBatSimCells.setMinimumSize(QtCore.QSize(100, 25))
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei")
        font.setBold(True)
        font.setWeight(75)
        self.spinBoxBatSimCells.setFont(font)
        self.spinBoxBatSimCells.setAlignment(QtCore.Qt.AlignCenter)
        self.spinBoxBatSimCells.setMinimum(1)
        self.spinBoxBatSimCells.setMaximum(6)
        self.spinBoxBatSimCells.setProperty("value", 1)
        self.spinBoxBatSimCells.setObjectName("spinBoxBatSimCells")
        self.gridLayoutBatteryParams.addWidget(self.spinBoxBatSimCells, 2, 1, 1, 1)
        self.verticalLayout_25.addWidget(self.groupBoxBatteryParams)
        spacerItem5 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_25.addItem(spacerItem5)
        self.scrollAreaBatSim.setWidget(self.scrollAreaWidgetContents_5)
        self.verticalLayout_26.addWidget(self.scrollAreaBatSim)
        self.tabWidget.addTab(self.tabBat, "")
        self.tab = QtWidgets.QWidget()
        self.tab.setObjectName("tab")
        self.verticalLayout_15 = QtWidgets.QVBoxLayout(self.tab)
        self.verticalLayout_15.setObjectName("verticalLayout_15")
        self.groupBoxChargeConfig_5 = QtWidgets.QGroupBox(self.tab)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Minimum)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.groupBoxChargeConfig_5.sizePolicy().hasHeightForWidth())
        self.groupBoxChargeConfig_5.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei")
        font.setBold(True)
        font.setWeight(75)
        self.groupBoxChargeConfig_5.setFont(font)
        self.groupBoxChargeConfig_5.setObjectName("groupBoxChargeConfig_5")
        self.verticalLayoutChargeConfig_5 = QtWidgets.QVBoxLayout(self.groupBoxChargeConfig_5)
        self.verticalLayoutChargeConfig_5.setSpacing(6)
        self.verticalLayoutChargeConfig_5.setObjectName("verticalLayoutChargeConfig_5")
        self.gridLayout = QtWidgets.QGridLayout()
        self.gridLayout.setObjectName("gridLayout")
        self.ext_eload_rb = QtWidgets.QRadioButton(self.groupBoxChargeConfig_5)
        self.ext_eload_rb.setObjectName("ext_eload_rb")
        self.gridLayout.addWidget(self.ext_eload_rb, 0, 1, 1, 1)
        self.inter_eload_rb = QtWidgets.QRadioButton(self.groupBoxChargeConfig_5)
        self.inter_eload_rb.setObjectName("inter_eload_rb")
        self.gridLayout.addWidget(self.inter_eload_rb, 0, 0, 1, 1)
        self.verticalLayoutChargeConfig_5.addLayout(self.gridLayout)
        self.verticalLayout_15.addWidget(self.groupBoxChargeConfig_5)
        self.groupBox = QtWidgets.QGroupBox(self.tab)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Minimum)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.groupBox.sizePolicy().hasHeightForWidth())
        self.groupBox.setSizePolicy(sizePolicy)
        self.groupBox.setObjectName("groupBox")
        self.verticalLayout_4 = QtWidgets.QVBoxLayout(self.groupBox)
        self.verticalLayout_4.setObjectName("verticalLayout_4")
        self.horizontalLayout_15 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_15.setObjectName("horizontalLayout_15")
        self.label_21 = QtWidgets.QLabel(self.groupBox)
        self.label_21.setAlignment(QtCore.Qt.AlignCenter)
        self.label_21.setObjectName("label_21")
        self.horizontalLayout_15.addWidget(self.label_21)
        self.fw_ver_lb = QtWidgets.QLabel(self.groupBox)
        self.fw_ver_lb.setObjectName("fw_ver_lb")
        self.horizontalLayout_15.addWidget(self.fw_ver_lb)
        self.verticalLayout_4.addLayout(self.horizontalLayout_15)
        self.horizontalLayout_14 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_14.setObjectName("horizontalLayout_14")
        self.label_15 = QtWidgets.QLabel(self.groupBox)
        self.label_15.setAlignment(QtCore.Qt.AlignCenter)
        self.label_15.setObjectName("label_15")
        self.horizontalLayout_14.addWidget(self.label_15)
        self.bl_ver = QtWidgets.QLabel(self.groupBox)
        self.bl_ver.setObjectName("bl_ver")
        self.horizontalLayout_14.addWidget(self.bl_ver)
        self.verticalLayout_4.addLayout(self.horizontalLayout_14)
        self.horizontalLayout_18 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_18.setObjectName("horizontalLayout_18")
        self.label_9 = QtWidgets.QLabel(self.groupBox)
        self.label_9.setAlignment(QtCore.Qt.AlignCenter)
        self.label_9.setObjectName("label_9")
        self.horizontalLayout_18.addWidget(self.label_9)
        self.hw_lb = QtWidgets.QLabel(self.groupBox)
        self.hw_lb.setObjectName("hw_lb")
        self.horizontalLayout_18.addWidget(self.hw_lb)
        self.verticalLayout_4.addLayout(self.horizontalLayout_18)
        self.check_ver_bt = QtWidgets.QPushButton(self.groupBox)
        self.check_ver_bt.setObjectName("check_ver_bt")
        self.verticalLayout_4.addWidget(self.check_ver_bt)
        self.horizontalLayout_9 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_9.setObjectName("horizontalLayout_9")
        self.SN_let = QtWidgets.QLineEdit(self.groupBox)
        self.SN_let.setObjectName("SN_let")
        self.horizontalLayout_9.addWidget(self.SN_let)
        self.check_sn_bt = QtWidgets.QPushButton(self.groupBox)
        self.check_sn_bt.setObjectName("check_sn_bt")
        self.horizontalLayout_9.addWidget(self.check_sn_bt)
        self.verticalLayout_4.addLayout(self.horizontalLayout_9)
        self.verticalLayout_15.addWidget(self.groupBox)
        self.groupBoxChargeConfig_3 = QtWidgets.QGroupBox(self.tab)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Minimum)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.groupBoxChargeConfig_3.sizePolicy().hasHeightForWidth())
        self.groupBoxChargeConfig_3.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei")
        font.setBold(True)
        font.setWeight(75)
        self.groupBoxChargeConfig_3.setFont(font)
        self.groupBoxChargeConfig_3.setObjectName("groupBoxChargeConfig_3")
        self.verticalLayout_21 = QtWidgets.QVBoxLayout(self.groupBoxChargeConfig_3)
        self.verticalLayout_21.setObjectName("verticalLayout_21")
        self.horizontalLayout_10 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_10.setObjectName("horizontalLayout_10")
        self.label_36 = QtWidgets.QLabel(self.groupBoxChargeConfig_3)
        self.label_36.setAlignment(QtCore.Qt.AlignCenter)
        self.label_36.setObjectName("label_36")
        self.horizontalLayout_10.addWidget(self.label_36)
        self.vol_comp_sb = QtWidgets.QDoubleSpinBox(self.groupBoxChargeConfig_3)
        self.vol_comp_sb.setObjectName("vol_comp_sb")
        self.horizontalLayout_10.addWidget(self.vol_comp_sb)
        self.vol_comp_bt = QtWidgets.QPushButton(self.groupBoxChargeConfig_3)
        self.vol_comp_bt.setObjectName("vol_comp_bt")
        self.horizontalLayout_10.addWidget(self.vol_comp_bt)
        self.verticalLayout_21.addLayout(self.horizontalLayout_10)
        self.horizontalLayout_8 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_8.setObjectName("horizontalLayout_8")
        self.label_45 = QtWidgets.QLabel(self.groupBoxChargeConfig_3)
        self.label_45.setAlignment(QtCore.Qt.AlignCenter)
        self.label_45.setObjectName("label_45")
        self.horizontalLayout_8.addWidget(self.label_45)
        self.curr_comp_sb = QtWidgets.QDoubleSpinBox(self.groupBoxChargeConfig_3)
        self.curr_comp_sb.setMinimum(-110.0)
        self.curr_comp_sb.setObjectName("curr_comp_sb")
        self.horizontalLayout_8.addWidget(self.curr_comp_sb)
        self.curr_comp_bt = QtWidgets.QPushButton(self.groupBoxChargeConfig_3)
        self.curr_comp_bt.setObjectName("curr_comp_bt")
        self.horizontalLayout_8.addWidget(self.curr_comp_bt)
        self.verticalLayout_21.addLayout(self.horizontalLayout_8)
        self.horizontalLayout_11 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_11.setObjectName("horizontalLayout_11")
        self.label_52 = QtWidgets.QLabel(self.groupBoxChargeConfig_3)
        self.label_52.setAlignment(QtCore.Qt.AlignCenter)
        self.label_52.setObjectName("label_52")
        self.horizontalLayout_11.addWidget(self.label_52)
        self.freq_comp_sb = QtWidgets.QDoubleSpinBox(self.groupBoxChargeConfig_3)
        self.freq_comp_sb.setObjectName("freq_comp_sb")
        self.horizontalLayout_11.addWidget(self.freq_comp_sb)
        self.freq_comp_bt = QtWidgets.QPushButton(self.groupBoxChargeConfig_3)
        self.freq_comp_bt.setObjectName("freq_comp_bt")
        self.horizontalLayout_11.addWidget(self.freq_comp_bt)
        self.verticalLayout_21.addLayout(self.horizontalLayout_11)
        self.line_12 = QtWidgets.QFrame(self.groupBoxChargeConfig_3)
        self.line_12.setFrameShape(QtWidgets.QFrame.HLine)
        self.line_12.setFrameShadow(QtWidgets.QFrame.Sunken)
        self.line_12.setObjectName("line_12")
        self.verticalLayout_21.addWidget(self.line_12)
        self.factor_reset_bt = QtWidgets.QPushButton(self.groupBoxChargeConfig_3)
        self.factor_reset_bt.setMinimumSize(QtCore.QSize(0, 30))
        self.factor_reset_bt.setObjectName("factor_reset_bt")
        self.verticalLayout_21.addWidget(self.factor_reset_bt)
        self.verticalLayout_15.addWidget(self.groupBoxChargeConfig_3)
        spacerItem6 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_15.addItem(spacerItem6)
        self.tabWidget.addTab(self.tab, "")
        self.tab_2 = QtWidgets.QWidget()
        self.tab_2.setObjectName("tab_2")
        self.verticalLayout_29 = QtWidgets.QVBoxLayout(self.tab_2)
        self.verticalLayout_29.setObjectName("verticalLayout_29")
        self.groupBox_4 = QtWidgets.QGroupBox(self.tab_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Minimum)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.groupBox_4.sizePolicy().hasHeightForWidth())
        self.groupBox_4.setSizePolicy(sizePolicy)
        self.groupBox_4.setObjectName("groupBox_4")
        self.verticalLayout_9 = QtWidgets.QVBoxLayout(self.groupBox_4)
        self.verticalLayout_9.setObjectName("verticalLayout_9")
        self.spinBox = QtWidgets.QSpinBox(self.groupBox_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Minimum)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.spinBox.sizePolicy().hasHeightForWidth())
        self.spinBox.setSizePolicy(sizePolicy)
        self.spinBox.setObjectName("spinBox")
        self.verticalLayout_9.addWidget(self.spinBox)
        self.spinBox_2 = QtWidgets.QSpinBox(self.groupBox_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Minimum)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.spinBox_2.sizePolicy().hasHeightForWidth())
        self.spinBox_2.setSizePolicy(sizePolicy)
        self.spinBox_2.setObjectName("spinBox_2")
        self.verticalLayout_9.addWidget(self.spinBox_2)
        self.radioButton = QtWidgets.QRadioButton(self.groupBox_4)
        self.radioButton.setAutoFillBackground(False)
        self.radioButton.setObjectName("radioButton")
        self.verticalLayout_9.addWidget(self.radioButton)
        self.radioButton_2 = QtWidgets.QRadioButton(self.groupBox_4)
        self.radioButton_2.setObjectName("radioButton_2")
        self.verticalLayout_9.addWidget(self.radioButton_2)
        self.radioButton_3 = QtWidgets.QRadioButton(self.groupBox_4)
        self.radioButton_3.setObjectName("radioButton_3")
        self.verticalLayout_9.addWidget(self.radioButton_3)
        self.verticalLayout_29.addWidget(self.groupBox_4)
        self.groupBox_6 = QtWidgets.QGroupBox(self.tab_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Minimum)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.groupBox_6.sizePolicy().hasHeightForWidth())
        self.groupBox_6.setSizePolicy(sizePolicy)
        self.groupBox_6.setObjectName("groupBox_6")
        self.verticalLayout_28 = QtWidgets.QVBoxLayout(self.groupBox_6)
        self.verticalLayout_28.setObjectName("verticalLayout_28")
        self.horizontalLayout_29 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_29.setObjectName("horizontalLayout_29")
        self.label_44 = QtWidgets.QLabel(self.groupBox_6)
        self.label_44.setObjectName("label_44")
        self.horizontalLayout_29.addWidget(self.label_44)
        self.comboSampleRate = QtWidgets.QComboBox(self.groupBox_6)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.comboSampleRate.sizePolicy().hasHeightForWidth())
        self.comboSampleRate.setSizePolicy(sizePolicy)
        self.comboSampleRate.setMinimumSize(QtCore.QSize(80, 0))
        self.comboSampleRate.setMaximumSize(QtCore.QSize(120, 16777215))
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei")
        self.comboSampleRate.setFont(font)
        self.comboSampleRate.setObjectName("comboSampleRate")
        self.comboSampleRate.addItem("")
        self.comboSampleRate.addItem("")
        self.comboSampleRate.addItem("")
        self.comboSampleRate.addItem("")
        self.horizontalLayout_29.addWidget(self.comboSampleRate)
        self.verticalLayout_28.addLayout(self.horizontalLayout_29)
        self.horizontalLayout_25 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_25.setObjectName("horizontalLayout_25")
        self.storage_depth_sp = QtWidgets.QSpinBox(self.groupBox_6)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Minimum)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.storage_depth_sp.sizePolicy().hasHeightForWidth())
        self.storage_depth_sp.setSizePolicy(sizePolicy)
        self.storage_depth_sp.setMaximum(2000)
        self.storage_depth_sp.setSingleStep(10)
        self.storage_depth_sp.setProperty("value", 20)
        self.storage_depth_sp.setObjectName("storage_depth_sp")
        self.horizontalLayout_25.addWidget(self.storage_depth_sp)
        self.label_18 = QtWidgets.QLabel(self.groupBox_6)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Minimum)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_18.sizePolicy().hasHeightForWidth())
        self.label_18.setSizePolicy(sizePolicy)
        self.label_18.setAlignment(QtCore.Qt.AlignCenter)
        self.label_18.setObjectName("label_18")
        self.horizontalLayout_25.addWidget(self.label_18)
        self.verticalLayout_28.addLayout(self.horizontalLayout_25)
        self.horizontalLayout_26 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_26.setObjectName("horizontalLayout_26")
        self.label_23 = QtWidgets.QLabel(self.groupBox_6)
        self.label_23.setAlignment(QtCore.Qt.AlignCenter)
        self.label_23.setObjectName("label_23")
        self.horizontalLayout_26.addWidget(self.label_23)
        self.record_duration_lb = QtWidgets.QLabel(self.groupBox_6)
        self.record_duration_lb.setAlignment(QtCore.Qt.AlignCenter)
        self.record_duration_lb.setObjectName("record_duration_lb")
        self.horizontalLayout_26.addWidget(self.record_duration_lb)
        self.verticalLayout_28.addLayout(self.horizontalLayout_26)
        self.horizontalLayout_28 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_28.setObjectName("horizontalLayout_28")
        self.label_42 = QtWidgets.QLabel(self.groupBox_6)
        self.label_42.setAlignment(QtCore.Qt.AlignCenter)
        self.label_42.setObjectName("label_42")
        self.horizontalLayout_28.addWidget(self.label_42)
        self.num_samples_lb = QtWidgets.QLabel(self.groupBox_6)
        self.num_samples_lb.setAlignment(QtCore.Qt.AlignCenter)
        self.num_samples_lb.setObjectName("num_samples_lb")
        self.horizontalLayout_28.addWidget(self.num_samples_lb)
        self.label_14 = QtWidgets.QLabel(self.groupBox_6)
        self.label_14.setObjectName("label_14")
        self.horizontalLayout_28.addWidget(self.label_14)
        self.max_samples_lb = QtWidgets.QLabel(self.groupBox_6)
        self.max_samples_lb.setObjectName("max_samples_lb")
        self.horizontalLayout_28.addWidget(self.max_samples_lb)
        self.verticalLayout_28.addLayout(self.horizontalLayout_28)
        self.verticalLayout_29.addWidget(self.groupBox_6)
        spacerItem7 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_29.addItem(spacerItem7)
        self.tabWidget.addTab(self.tab_2, "")
        self.verticalLayout_27.addWidget(self.tabWidget)
        self.verticalLayout_6.addWidget(self.frame)
        self.verticalLayout_11.addLayout(self.verticalLayout_6)
        self.frameSystemSetting = QtWidgets.QFrame(self.frameOutputSetting)
        self.frameSystemSetting.setMinimumSize(QtCore.QSize(0, 0))
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei")
        self.frameSystemSetting.setFont(font)
        self.frameSystemSetting.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frameSystemSetting.setFrameShadow(QtWidgets.QFrame.Plain)
        self.frameSystemSetting.setObjectName("frameSystemSetting")
        self.verticalLayout_13 = QtWidgets.QVBoxLayout(self.frameSystemSetting)
        self.verticalLayout_13.setContentsMargins(3, 3, 3, 3)
        self.verticalLayout_13.setObjectName("verticalLayout_13")
        self.horizontalLayout_54 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_54.setObjectName("horizontalLayout_54")
        self.verticalLayout_13.addLayout(self.horizontalLayout_54)
        self.verticalLayout_14 = QtWidgets.QVBoxLayout()
        self.verticalLayout_14.setSpacing(3)
        self.verticalLayout_14.setObjectName("verticalLayout_14")
        self.ProtocolButton = QtWidgets.QPushButton(self.frameSystemSetting)
        self.ProtocolButton.setObjectName("ProtocolButton")
        self.verticalLayout_14.addWidget(self.ProtocolButton)
        self.btnUpdatings = QtWidgets.QPushButton(self.frameSystemSetting)
        self.btnUpdatings.setMinimumSize(QtCore.QSize(30, 0))
        self.btnUpdatings.setMaximumSize(QtCore.QSize(999, 16777215))
        self.btnUpdatings.setObjectName("btnUpdatings")
        self.verticalLayout_14.addWidget(self.btnUpdatings)
        self.horizontalLayout_21 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_21.setContentsMargins(-1, 0, -1, -1)
        self.horizontalLayout_21.setObjectName("horizontalLayout_21")
        self.btnGraphics = QtWidgets.QPushButton(self.frameSystemSetting)
        self.btnGraphics.setMinimumSize(QtCore.QSize(0, 30))
        self.btnGraphics.setMaximumSize(QtCore.QSize(999, 16777215))
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei")
        self.btnGraphics.setFont(font)
        self.btnGraphics.setObjectName("btnGraphics")
        self.horizontalLayout_21.addWidget(self.btnGraphics)
        self.btnSettings = QtWidgets.QPushButton(self.frameSystemSetting)
        self.btnSettings.setMinimumSize(QtCore.QSize(0, 30))
        self.btnSettings.setMaximumSize(QtCore.QSize(999, 16777215))
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei")
        self.btnSettings.setFont(font)
        self.btnSettings.setObjectName("btnSettings")
        self.horizontalLayout_21.addWidget(self.btnSettings)
        self.verticalLayout_14.addLayout(self.horizontalLayout_21)
        self.horizontalLayout_12 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_12.setObjectName("horizontalLayout_12")
        self.labelConnectState = QtWidgets.QLabel(self.frameSystemSetting)
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei")
        font.setBold(False)
        font.setItalic(False)
        font.setWeight(50)
        self.labelConnectState.setFont(font)
        self.labelConnectState.setTextFormat(QtCore.Qt.AutoText)
        self.labelConnectState.setAlignment(QtCore.Qt.AlignCenter)
        self.labelConnectState.setObjectName("labelConnectState")
        self.horizontalLayout_12.addWidget(self.labelConnectState)
        self.btnConnect = QtWidgets.QPushButton(self.frameSystemSetting)
        self.btnConnect.setMinimumSize(QtCore.QSize(0, 30))
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei")
        self.btnConnect.setFont(font)
        self.btnConnect.setObjectName("btnConnect")
        self.horizontalLayout_12.addWidget(self.btnConnect)
        self.horizontalLayout_12.setStretch(0, 1)
        self.horizontalLayout_12.setStretch(1, 1)
        self.verticalLayout_14.addLayout(self.horizontalLayout_12)
        self.verticalLayout_13.addLayout(self.verticalLayout_14)
        self.verticalLayout_11.addWidget(self.frameSystemSetting)
        self.verticalLayout_settings.addWidget(self.frameOutputSetting)
        self.horizontalLayout_bottom.addLayout(self.verticalLayout_settings)
        self.horizontalLayout_bottom.setStretch(0, 1)
        self.horizontalLayout_13.addLayout(self.horizontalLayout_bottom)
        self.horizontalLayout_13.setStretch(1, 1)
        self.verticalLayout_8.addLayout(self.horizontalLayout_13)
        self.verticalLayout_8.setStretch(2, 1)
        MainWindow.setCentralWidget(self.centralwidget)

        self.retranslateUi(MainWindow)
        self.comboGraph2Data.setCurrentIndex(1)
        self.tabWidget.setCurrentIndex(1)
        QtCore.QMetaObject.connectSlotsByName(MainWindow)

    def retranslateUi(self, MainWindow):
        _translate = QtCore.QCoreApplication.translate
        MainWindow.setWindowTitle(_translate("MainWindow", "WPX-01 无线充电测试仪上位机"))
        self.label.setText(_translate("MainWindow", "  电压"))
        self.label_2.setText(_translate("MainWindow", "  电流"))
        self.label_3.setText(_translate("MainWindow", "  功率"))
        self.label_4.setText(_translate("MainWindow", "  容量"))
        self.label_5.setText(_translate("MainWindow", "平均功率"))
        self.label_6.setText(_translate("MainWindow", "能量"))
        self.label_13.setText(_translate("MainWindow", "X/Y数据:"))
        self.comboGraph1Data.setToolTip(_translate("MainWindow", "上窗口数据"))
        self.comboGraph1Data.setItemText(0, _translate("MainWindow", "电压"))
        self.comboGraph1Data.setItemText(1, _translate("MainWindow", "电流"))
        self.comboGraph1Data.setItemText(2, _translate("MainWindow", "功率"))
        self.comboGraph1Data.setItemText(3, _translate("MainWindow", "核心温度"))
        self.comboGraph1Data.setItemText(4, _translate("MainWindow", "负载温度"))
        self.comboGraph1Data.setItemText(5, _translate("MainWindow", "外部探头温度"))
        self.comboGraph1Data.setItemText(6, _translate("MainWindow", "整流桥温度"))
        self.comboGraph1Data.setItemText(7, _translate("MainWindow", "线圈温度"))
        self.comboGraph1Data.setItemText(8, _translate("MainWindow", "无"))
        self.comboGraph2Data.setToolTip(_translate("MainWindow", "下窗口数据"))
        self.comboGraph2Data.setItemText(0, _translate("MainWindow", "电压"))
        self.comboGraph2Data.setItemText(1, _translate("MainWindow", "电流"))
        self.comboGraph2Data.setItemText(2, _translate("MainWindow", "功率"))
        self.comboGraph2Data.setItemText(3, _translate("MainWindow", "核心温度"))
        self.comboGraph2Data.setItemText(4, _translate("MainWindow", "负载温度"))
        self.comboGraph2Data.setItemText(5, _translate("MainWindow", "外部探头温度"))
        self.comboGraph2Data.setItemText(6, _translate("MainWindow", "整流桥温度"))
        self.comboGraph2Data.setItemText(7, _translate("MainWindow", "线圈温度"))
        self.comboGraph2Data.setItemText(8, _translate("MainWindow", "无"))
        self.label_29.setText(_translate("MainWindow", "采样"))
        self.labelFps.setToolTip(_translate("MainWindow", "实时采样率"))
        self.labelFps.setText(_translate("MainWindow", "0.0Hz"))
        self.btnGraphRecord.setToolTip(_translate("MainWindow", "将原始数据记录到CSV文件，不受图形缓冲区限制"))
        self.btnGraphRecord.setText(_translate("MainWindow", "录制"))
        self.btnGraphDump.setToolTip(_translate("MainWindow", "导出当前数据缓冲区的有效数据"))
        self.btnGraphDump.setText(_translate("MainWindow", "导出"))
        self.btnGraphAutoScale.setToolTip(_translate("MainWindow", "切换数据波形是否自动适应窗口"))
        self.btnGraphAutoScale.setText(_translate("MainWindow", "适应"))
        self.btnRecordFloatWindow.setToolTip(_translate("MainWindow", "切换数据监控悬浮窗"))
        self.btnRecordFloatWindow.setText(_translate("MainWindow", "浮窗"))
        self.btnRecordClear.setToolTip(_translate("MainWindow", "清零平均功率和能量累计"))
        self.btnRecordClear.setText(_translate("MainWindow", "回零"))
        self.btnGraphClear.setToolTip(_translate("MainWindow", "清空波形数据缓冲区"))
        self.btnGraphClear.setText(_translate("MainWindow", "清空"))
        self.btnGraphKeep.setToolTip(_translate("MainWindow", "停止数据波形刷新（数据缓冲区仍在更新）"))
        self.btnGraphKeep.setText(_translate("MainWindow", "暂停"))
        self.run_time_lb.setText(_translate("MainWindow", "00:00:00"))
        self.potential_power_lb.setText(_translate("MainWindow", "0W"))
        self.Coil_temp_bt.setText(_translate("MainWindow", "线圈温度:"))
        self.signal_strength_lb.setText(_translate("MainWindow", "0%"))
        self.label_16.setText(_translate("MainWindow", "握手功率:"))
        self.label_24.setText(_translate("MainWindow", "核心温度:"))
        self.label_39.setText(_translate("MainWindow", "RP:"))
        self.label_12.setText(_translate("MainWindow", "信号强度:"))
        self.label_30.setText(_translate("MainWindow", "负载温度:"))
        self.ext_probe_temp_lb.setText(_translate("MainWindow", "0℃"))
        self.label_26.setText(_translate("MainWindow", "外部探头温度:"))
        self.ce_lb.setText(_translate("MainWindow", "0"))
        self.rpp_lb.setText(_translate("MainWindow", "0"))
        self.coil_temp_lb.setText(_translate("MainWindow", "0℃"))
        self.eload_temp_lb.setText(_translate("MainWindow", "0℃"))
        self.label_31.setText(_translate("MainWindow", "整流桥温度:"))
        self.label_8.setText(_translate("MainWindow", "CE:"))
        self.freq_lb.setText(_translate("MainWindow", "0 Khz"))
        self.test_protocol_lb.setText(_translate("MainWindow", "-"))
        self.core_temp_lb.setText(_translate("MainWindow", "0℃"))
        self.nego_power_lb.setText(_translate("MainWindow", "0W"))
        self.label_7.setText(_translate("MainWindow", "工作频率:"))
        self.label_17.setText(_translate("MainWindow", "潜在功率:"))
        self.bridge_temp_lb.setText(_translate("MainWindow", "0℃"))
        self.label_11.setText(_translate("MainWindow", "握手协议:"))
        self.label_19.setText(_translate("MainWindow", "设备时间:"))
        self.groupBoxChargeConfig_4.setTitle(_translate("MainWindow", "🔋 协议配置"))
        self.set_protocol_lb.setText(_translate("MainWindow", "运行协议："))
        self.set_protocol_cb.setItemText(0, _translate("MainWindow", "BPP 5W"))
        self.set_protocol_cb.setItemText(1, _translate("MainWindow", "EPP 15W"))
        self.set_protocol_cb.setItemText(2, _translate("MainWindow", "Qi2 MPP 15W"))
        self.set_protocol_cb.setItemText(3, _translate("MainWindow", "Qi2.2 MPP 25W"))
        self.set_protocol_cb.setItemText(4, _translate("MainWindow", "Apple 7.5W"))
        self.set_protocol_cb.setItemText(5, _translate("MainWindow", "Apple MagSafe 15W"))
        self.set_protocol_cb.setItemText(6, _translate("MainWindow", "FAKE MagSafe"))
        self.set_protocol_cb.setItemText(7, _translate("MainWindow", "PPDE 10W"))
        self.set_protocol_cb.setItemText(8, _translate("MainWindow", "Sumsung 15W"))
        self.set_protocol_cb.setItemText(9, _translate("MainWindow", "Xiaomi "))
        self.set_protocol_cb.setItemText(10, _translate("MainWindow", "Huawei"))
        self.set_protocol_cb.setItemText(11, _translate("MainWindow", "OPPO"))
        self.set_protocol_cb.setItemText(12, _translate("MainWindow", "VIVO"))
        self.set_protocol_cb.setItemText(13, _translate("MainWindow", "Redmi"))
        self.set_protocol_cb.setItemText(14, _translate("MainWindow", "Realme"))
        self.set_protocol_cb.setItemText(15, _translate("MainWindow", "iQOO"))
        self.set_protocol_cb.setItemText(16, _translate("MainWindow", "Meizu"))
        self.set_protocol_cb.setItemText(17, _translate("MainWindow", "Honor"))
        self.set_protocol_cb.setItemText(18, _translate("MainWindow", "Generic"))
        self.set_protocol_bt.setText(_translate("MainWindow", "设置"))
        self.label_10.setText(_translate("MainWindow", "FOD补偿："))
        self.fod_offset_lb.setText(_translate("MainWindow", "+500"))
        self.set_fod_offset_bt.setText(_translate("MainWindow", "设置"))
        self.label_64.setText(_translate("MainWindow", "负载电压电流设置:(非必要请勿修改)"))
        self.set_volta_ld.setText(_translate("MainWindow", "6.0"))
        self.label_66.setText(_translate("MainWindow", "V"))
        self.set_current_ld.setText(_translate("MainWindow", "1.0"))
        self.label_67.setText(_translate("MainWindow", "A"))
        self.set_power_data_bt.setText(_translate("MainWindow", "设置"))
        self.send_bat_full_bt.setText(_translate("MainWindow", "发送电池充满指令"))
        self.groupBoxChargeControl_3.setTitle(_translate("MainWindow", "⚡ 协议控制"))
        self.send_ce_lb.setText(_translate("MainWindow", "Control Error:"))
        self.send_ce_sp.setToolTip(_translate("MainWindow", "发送所设置的CE大小"))
        self.send_ce_bt.setText(_translate("MainWindow", "发送"))
        self.send_ept_lb.setText(_translate("MainWindow", "EPT:"))
        self.send_ept_cb.setItemText(0, _translate("MainWindow", "充满 / 01"))
        self.send_ept_cb.setItemText(1, _translate("MainWindow", "故障 / 02"))
        self.send_ept_cb.setItemText(2, _translate("MainWindow", "过温 / 03"))
        self.send_ept_cb.setItemText(3, _translate("MainWindow", "过压 / 04"))
        self.send_ept_cb.setItemText(4, _translate("MainWindow", "过流 / 05"))
        self.send_ept_cb.setItemText(5, _translate("MainWindow", "电池故障 / 06"))
        self.send_ept_cb.setItemText(6, _translate("MainWindow", "工作点异常 / 08"))
        self.send_ept_cb.setItemText(7, _translate("MainWindow", "协商异常 / 0A"))
        self.send_ept_cb.setItemText(8, _translate("MainWindow", "重启 / 0B"))
        self.send_ept_cb.setItemText(9, _translate("MainWindow", "Reping / 0C"))
        self.send_ept_cb.setItemText(10, _translate("MainWindow", "NFC / 0D"))
        self.send_ept_cb.setItemText(11, _translate("MainWindow", "NA / 0E"))
        self.send_ept_cb.setItemText(12, _translate("MainWindow", "NA / 0F"))
        self.send_ept_bt.setText(_translate("MainWindow", "发送"))
        self.label_65.setText(_translate("MainWindow", "指定数据包：（16进制，空格分隔）"))
        self.protocol_data_ld.setText(_translate("MainWindow", "18 01 19"))
        self.protocol_send_data_bt.setText(_translate("MainWindow", "发送"))
        self.start_auto_test_bt.setText(_translate("MainWindow", "启动自动化协议检测"))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tabPower), _translate("MainWindow", "调试"))
        self.tabWidget.setTabToolTip(self.tabWidget.indexOf(self.tabPower), _translate("MainWindow", "调试设置"))
        self.groupBox_2.setTitle(_translate("MainWindow", "EPP 数据"))
        self.support_dup_lb.setText(_translate("MainWindow", "0"))
        self.support_ob_lb.setText(_translate("MainWindow", "0"))
        self.manufacturer_id_lb.setText(_translate("MainWindow", "0"))
        self.label_22.setText(_translate("MainWindow", "制造商 ID:"))
        self.Coil_temp_bt_2.setText(_translate("MainWindow", "双工通信:"))
        self.label_27.setText(_translate("MainWindow", "NRS 比例:"))
        self.wpid_value_lb.setText(_translate("MainWindow", "0"))
        self.support_ar_lb.setText(_translate("MainWindow", "0"))
        self.label_34.setText(_translate("MainWindow", "带外通信:"))
        self.label_25.setText(_translate("MainWindow", "WPID:"))
        self.label_28.setText(_translate("MainWindow", "支持鉴权:"))
        self.nrs_value_lb.setText(_translate("MainWindow", "0"))
        self.label_20.setText(_translate("MainWindow", "TX Qi版本:"))
        self.qi_version_lb.setText(_translate("MainWindow", "0"))
        self.groupBox_3.setTitle(_translate("MainWindow", "MPP 数据"))
        self.app_value_lb.setText(_translate("MainWindow", "0"))
        self.support_calibration_lb.setText(_translate("MainWindow", "0"))
        self.buffer_size_lb.setText(_translate("MainWindow", "0"))
        self.label_51.setText(_translate("MainWindow", "设备ID:"))
        self.uid_value_lb.setText(_translate("MainWindow", "0"))
        self.label_49.setText(_translate("MainWindow", "并发数据流:"))
        self.power_limit_reason_lb.setText(_translate("MainWindow", "0"))
        self.device_id_lb.setText(_translate("MainWindow", "0"))
        self.Coil_temp_bt_3.setText(_translate("MainWindow", "APP:"))
        self.concurrent_streams_lb.setText(_translate("MainWindow", "0"))
        self.g_coil_rx_lb.setText(_translate("MainWindow", "0"))
        self.label_46.setText(_translate("MainWindow", "支持校准:"))
        self.label_54.setText(_translate("MainWindow", "功率限制原因:"))
        self.label_55.setText(_translate("MainWindow", "g_goil_rx:"))
        self.label_56.setText(_translate("MainWindow", "UID:"))
        self.label_47.setText(_translate("MainWindow", "缓存大小:"))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tabWaveGen), _translate("MainWindow", "协议"))
        self.tabWidget.setTabToolTip(self.tabWidget.indexOf(self.tabWaveGen), _translate("MainWindow", "协议信息查看"))
        self.btnBatSim.setText(_translate("MainWindow", "点击开启功能"))
        self.labelBatSimTime.setText(_translate("MainWindow", "Charge Time: 00:00:00"))
        self.groupBoxChargeConfig.setTitle(_translate("MainWindow", "🔋 充电配置"))
        self.btnBatSimLoad.setToolTip(_translate("MainWindow", "从文件加载充电曲线数据"))
        self.btnBatSimLoad.setText(_translate("MainWindow", "加载曲线"))
        self.btnBatSimPreview.setToolTip(_translate("MainWindow", "预览当前充电曲线"))
        self.btnBatSimPreview.setText(_translate("MainWindow", "查看曲线"))
        self.label_32.setText(_translate("MainWindow", "充电曲线"))
        self.comboBatSimCurve.setToolTip(_translate("MainWindow", "选择要使用的充电曲线类型"))
        self.groupBoxChargeControl.setTitle(_translate("MainWindow", "⚡ 充电控制"))
        self.label_37.setText(_translate("MainWindow", "目标电量"))
        self.label_35.setText(_translate("MainWindow", "当前电量"))
        self.spinBoxBatSimCurrent.setToolTip(_translate("MainWindow", "设置电池当前电量百分比"))
        self.spinBoxBatSimCurrent.setSuffix(_translate("MainWindow", "%"))
        self.spinBoxBatSimStop.setToolTip(_translate("MainWindow", "设置充电目标电量百分比"))
        self.spinBoxBatSimStop.setSuffix(_translate("MainWindow", "%"))
        self.label_38.setText(_translate("MainWindow", "执行频率"))
        self.spinBoxBatSimLoopFreq.setToolTip(_translate("MainWindow", "设置充电模拟计算频率"))
        self.spinBoxBatSimLoopFreq.setSuffix(_translate("MainWindow", "Hz"))
        self.groupBoxBatteryParams.setTitle(_translate("MainWindow", "🔧 电池参数"))
        self.label_33.setText(_translate("MainWindow", "单节容量"))
        self.spinBoxBatSimCap.setToolTip(_translate("MainWindow", "设置单节电池的能量容量"))
        self.spinBoxBatSimCap.setSuffix(_translate("MainWindow", "Wh"))
        self.label_40.setText(_translate("MainWindow", "单节内阻"))
        self.spinBoxBatSimRes.setToolTip(_translate("MainWindow", "设置单节电池的内阻值"))
        self.spinBoxBatSimRes.setSuffix(_translate("MainWindow", "mΩ"))
        self.label_41.setText(_translate("MainWindow", "串联节数"))
        self.spinBoxBatSimCells.setToolTip(_translate("MainWindow", "设置电池串联的节数"))
        self.spinBoxBatSimCells.setSuffix(_translate("MainWindow", "S"))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tabBat), _translate("MainWindow", "充电模拟"))
        self.tabWidget.setTabToolTip(self.tabWidget.indexOf(self.tabBat), _translate("MainWindow", "电池充电模拟测试"))
        self.groupBoxChargeConfig_5.setTitle(_translate("MainWindow", "负载配置"))
        self.ext_eload_rb.setText(_translate("MainWindow", "外部负载"))
        self.inter_eload_rb.setText(_translate("MainWindow", "内部负载"))
        self.groupBox.setTitle(_translate("MainWindow", "设备信息"))
        self.label_21.setText(_translate("MainWindow", "固件版本："))
        self.fw_ver_lb.setText(_translate("MainWindow", "0.0.0"))
        self.label_15.setText(_translate("MainWindow", "内核版本："))
        self.bl_ver.setText(_translate("MainWindow", "0.0.0"))
        self.label_9.setText(_translate("MainWindow", "硬件版本："))
        self.hw_lb.setText(_translate("MainWindow", "0.0.0"))
        self.check_ver_bt.setText(_translate("MainWindow", "查询版本"))
        self.check_sn_bt.setText(_translate("MainWindow", "查询 SN"))
        self.groupBoxChargeConfig_3.setTitle(_translate("MainWindow", "系统设置"))
        self.label_36.setText(_translate("MainWindow", "电压补偿："))
        self.vol_comp_bt.setText(_translate("MainWindow", "设置"))
        self.label_45.setText(_translate("MainWindow", "电流补偿："))
        self.curr_comp_bt.setText(_translate("MainWindow", "设置"))
        self.label_52.setText(_translate("MainWindow", "频率补偿："))
        self.freq_comp_bt.setText(_translate("MainWindow", "设置"))
        self.factor_reset_bt.setText(_translate("MainWindow", "恢复出厂设置"))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab), _translate("MainWindow", "设置"))
        self.groupBox_4.setTitle(_translate("MainWindow", "在线数据/条件"))
        self.radioButton.setText(_translate("MainWindow", "满足条件自动停止"))
        self.radioButton_2.setText(_translate("MainWindow", "使用系统时间开始记录"))
        self.radioButton_3.setText(_translate("MainWindow", "边采边保存"))
        self.groupBox_6.setTitle(_translate("MainWindow", "存储深度"))
        self.label_44.setText(_translate("MainWindow", "采样率："))
        self.comboSampleRate.setItemText(0, _translate("MainWindow", "1SPS"))
        self.comboSampleRate.setItemText(1, _translate("MainWindow", "10SPS"))
        self.comboSampleRate.setItemText(2, _translate("MainWindow", "50SPS"))
        self.comboSampleRate.setItemText(3, _translate("MainWindow", "1KSPS"))
        self.label_18.setText(_translate("MainWindow", "MB"))
        self.label_23.setText(_translate("MainWindow", "缓存数据："))
        self.record_duration_lb.setText(_translate("MainWindow", "xxx"))
        self.label_42.setText(_translate("MainWindow", "当前记录数据："))
        self.num_samples_lb.setText(_translate("MainWindow", "0"))
        self.label_14.setText(_translate("MainWindow", "/"))
        self.max_samples_lb.setText(_translate("MainWindow", "100000"))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab_2), _translate("MainWindow", "图表"))
        self.ProtocolButton.setText(_translate("MainWindow", "Qi协议分析仪"))
        self.btnUpdatings.setText(_translate("MainWindow", "软件/固件更新"))
        self.btnGraphics.setText(_translate("MainWindow", "图形设置"))
        self.btnSettings.setText(_translate("MainWindow", "⚙系统设置"))
        self.labelConnectState.setText(_translate("MainWindow", "未连接"))
        self.btnConnect.setText(_translate("MainWindow", "连接/断开"))
from mdp_gui_template.applestyledisplay import AppleStyleDisplay
from pyqtgraph import PlotWidget
