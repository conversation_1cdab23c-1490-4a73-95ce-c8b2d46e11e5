"""
系统资源保护模块
用于检查系统资源是否足够支持长时间高采样率数据记录，防止因内存不足导致程序崩溃
"""

import psutil
import sys
import platform
import time
from loguru import logger
from typing import Tuple, Dict, Optional


class SystemResourceProtection:
    """系统资源保护类"""
    
    # 常量定义
    MIN_PHYSICAL_MEMORY_GB = 2.0  # 最小物理内存要求（GB）
    MIN_AVAILABLE_MEMORY_GB = 2.0  # 最小可用内存要求（GB）
    MIN_DISK_SPACE_GB = 10.0      # 最小磁盘空间要求（GB）
    BYTES_PER_SAMPLE = 80         # 每个采样点的内存占用（字节）
    WARNING_MEMORY_THRESHOLD = 0.8  # 内存使用率警告阈值
    
    @staticmethod
    def get_system_info() -> Dict:
        """获取系统信息"""
        try:
            # 获取内存信息
            memory = psutil.virtual_memory()
            
            # 获取磁盘信息
            disk = psutil.disk_usage('/')
            if platform.system() == 'Windows':
                disk = psutil.disk_usage('C:')
            
            # 获取CPU信息
            cpu_count = psutil.cpu_count()
            cpu_freq = psutil.cpu_freq()
            
            return {
                'memory': {
                    'total_gb': memory.total / (1024**3),
                    'available_gb': memory.available / (1024**3),
                    'used_gb': memory.used / (1024**3),
                    'percent': memory.percent
                },
                'disk': {
                    'total_gb': disk.total / (1024**3),
                    'free_gb': disk.free / (1024**3),
                    'used_gb': disk.used / (1024**3),
                    'percent': (disk.used / disk.total) * 100
                },
                'cpu': {
                    'count': cpu_count,
                    'freq_mhz': cpu_freq.current if cpu_freq else 0
                },
                'platform': platform.system(),
                'python_version': sys.version
            }
        except Exception as e:
            logger.error(f"获取系统信息失败: {e}")
            return {}
    
    @classmethod
    def check_memory_requirements(cls, sampling_rate: int, duration_hours: float) -> Tuple[bool, str, Dict]:
        """
        检查内存要求是否满足
        
        Args:
            sampling_rate: 采样率 (samples per second)
            duration_hours: 记录时长 (hours)
            
        Returns:
            (is_sufficient, message, details)
        """
        try:
            # 计算总采样点数
            total_samples = int(sampling_rate * duration_hours * 3600)
            
            # 计算所需内存（虚拟内存用于数据存储）
            required_virtual_memory_gb = (total_samples * cls.BYTES_PER_SAMPLE) / (1024**3)
            
            # 估算绘图所需的物理内存（每个数据点约需要额外16字节用于绘图缓存）
            required_physical_memory_gb = (total_samples * 16) / (1024**3)
            
            # 获取当前系统状态
            system_info = cls.get_system_info()
            if not system_info:
                return False, "无法获取系统信息", {}
            
            memory = system_info['memory']
            disk = system_info['disk']
            
            # 详细信息
            details = {
                'total_samples': total_samples,
                'required_virtual_memory_gb': required_virtual_memory_gb,
                'required_physical_memory_gb': required_physical_memory_gb,
                'available_memory_gb': memory['available_gb'],
                'free_disk_gb': disk['free_gb'],
                'system_info': system_info
            }
            
            # 检查物理内存
            if memory['available_gb'] < cls.MIN_AVAILABLE_MEMORY_GB:
                message = f"可用物理内存不足！\n"
                message += f"当前可用: {memory['available_gb']:.1f}GB\n"
                message += f"最低要求: {cls.MIN_AVAILABLE_MEMORY_GB}GB\n"
                message += f"建议关闭其他应用程序或增加物理内存"
                return False, message, details
            
            # 检查绘图内存需求
            estimated_plotting_memory = required_physical_memory_gb + 0.5  # 额外0.5GB用于绘图开销
            if memory['available_gb'] < estimated_plotting_memory:
                message = f"绘图所需内存可能不足！\n"
                message += f"预计需要: {estimated_plotting_memory:.1f}GB\n"
                message += f"当前可用: {memory['available_gb']:.1f}GB\n"
                message += f"长时间记录可能导致程序崩溃"
                return False, message, details
            
            # 检查虚拟内存（磁盘空间）
            if disk['free_gb'] < required_virtual_memory_gb + cls.MIN_DISK_SPACE_GB:
                message = f"磁盘空间不足！\n"
                message += f"数据存储需要: {required_virtual_memory_gb:.1f}GB\n"
                message += f"磁盘可用空间: {disk['free_gb']:.1f}GB\n"
                message += f"建议清理磁盘空间或降低采样率/时长"
                return False, message, details
            
            # 检查是否接近警告阈值
            memory_usage_after = (memory['used_gb'] + estimated_plotting_memory) / memory['total_gb']
            if memory_usage_after > cls.WARNING_MEMORY_THRESHOLD:
                message = f"内存使用率可能过高！\n"
                message += f"预计内存使用率: {memory_usage_after*100:.1f}%\n"
                message += f"建议降低采样率或缩短记录时长以确保稳定性"
                return False, message, details
            
            # 通过所有检查
            message = f"系统资源检查通过\n"
            message += f"数据存储需要: {required_virtual_memory_gb:.1f}GB 虚拟内存\n"
            message += f"绘图需要: {estimated_plotting_memory:.1f}GB 物理内存\n"
            message += f"预计内存使用率: {memory_usage_after*100:.1f}%"
            
            return True, message, details
            
        except Exception as e:
            logger.error(f"内存要求检查失败: {e}")
            return False, f"内存检查失败: {e}", {}
    
    @classmethod
    def suggest_optimal_settings(cls, target_duration_hours: float) -> Dict:
        """
        根据系统配置建议最优的采样设置
        
        Args:
            target_duration_hours: 目标记录时长 (hours)
            
        Returns:
            建议的设置参数
        """
        try:
            system_info = cls.get_system_info()
            if not system_info:
                return {}
            
            memory = system_info['memory']
            
            # 可用于数据存储的内存（保留2GB用于系统和绘图）
            available_for_data_gb = max(0, memory['available_gb'] - cls.MIN_AVAILABLE_MEMORY_GB)
            
            # 计算最大采样点数
            max_samples = int((available_for_data_gb * 1024**3) / cls.BYTES_PER_SAMPLE)
            
            # 根据目标时长计算最大采样率
            max_sample_rate = max_samples / (target_duration_hours * 3600)
            
            # 建议的采样率选项
            sample_rates = [1, 10, 50, 1000]  # SPS
            recommended_rate = 1
            
            for rate in sample_rates:
                if rate <= max_sample_rate:
                    recommended_rate = rate
                else:
                    break
            
            # 计算在推荐采样率下的实际记录时长
            actual_duration_hours = max_samples / (recommended_rate * 3600)
            
            return {
                'recommended_sample_rate': recommended_rate,
                'max_sample_rate': int(max_sample_rate),
                'actual_duration_hours': actual_duration_hours,
                'max_samples': max_samples,
                'available_memory_gb': available_for_data_gb,
                'system_memory_gb': memory['total_gb']
            }
            
        except Exception as e:
            logger.error(f"计算最优设置失败: {e}")
            return {}
    
    @classmethod
    def check_disk_performance(cls) -> Tuple[bool, str]:
        """
        检查磁盘性能是否适合高速数据记录
        
        Returns:
            (is_good_performance, message)
        """
        try:
            import time
            import tempfile
            import os
            
            # 测试磁盘写入性能
            test_data = b'x' * (1024 * 1024)  # 1MB测试数据
            test_file = os.path.join(tempfile.gettempdir(), 'wpx_disk_test.tmp')
            
            start_time = time.perf_counter()
            
            # 写入测试
            with open(test_file, 'wb') as f:
                for _ in range(10):  # 写入10MB
                    f.write(test_data)
                    f.flush()
                    os.fsync(f.fileno())  # 强制刷新到磁盘
            
            write_time = time.perf_counter() - start_time
            write_speed_mbps = 10 / write_time  # MB/s
            
            # 清理测试文件
            try:
                os.remove(test_file)
            except:
                pass
            
            # 判断性能
            if write_speed_mbps > 100:  # SSD级别性能
                message = f"磁盘性能优秀 ({write_speed_mbps:.1f} MB/s)\n适合高采样率长时间记录"
                return True, message
            elif write_speed_mbps > 30:  # 机械硬盘中等性能
                message = f"磁盘性能中等 ({write_speed_mbps:.1f} MB/s)\n建议使用中等采样率"
                return True, message
            else:  # 性能较差
                message = f"磁盘性能较差 ({write_speed_mbps:.1f} MB/s)\n不建议高采样率长时间记录"
                return False, message
                
        except Exception as e:
            logger.warning(f"磁盘性能测试失败: {e}")
            return True, "无法测试磁盘性能，请谨慎设置采样参数"
    
    @classmethod
    def monitor_runtime_resources(cls) -> Dict:
        """
        监控运行时资源使用情况
        
        Returns:
            资源使用情况字典
        """
        try:
            # 获取当前进程信息
            process = psutil.Process()
            
            # 内存使用
            memory_info = process.memory_info()
            memory_percent = process.memory_percent()
            
            # 系统内存
            system_memory = psutil.virtual_memory()
            
            # CPU使用率
            cpu_percent = process.cpu_percent()
            
            return {
                'process_memory_mb': memory_info.rss / (1024 * 1024),
                'process_memory_percent': memory_percent,
                'system_memory_available_gb': system_memory.available / (1024**3),
                'system_memory_percent': system_memory.percent,
                'cpu_percent': cpu_percent,
                'timestamp': time.time()
            }
            
        except Exception as e:
            logger.error(f"监控运行时资源失败: {e}")
            return {}
