<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>DialogSettings</class>
 <widget class="QDialog" name="DialogSettings">
  <property name="windowModality">
   <enum>Qt::WindowModal</enum>
  </property>
  <property name="enabled">
   <bool>true</bool>
  </property>
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>303</width>
    <height>460</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>303</width>
    <height>460</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>303</width>
    <height>460</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>系统设置</string>
  </property>
  <property name="sizeGripEnabled">
   <bool>false</bool>
  </property>
  <property name="modal">
   <bool>true</bool>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QLabel" name="label">
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>20</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>20</height>
      </size>
     </property>
     <property name="text">
      <string/>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QLabel" name="label_36">
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>28</height>
      </size>
     </property>
     <property name="font">
      <font>
       <family>Microsoft YaHei</family>
      </font>
     </property>
     <property name="text">
      <string>- 连接设置 -</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
     <property name="margin">
      <number>6</number>
     </property>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_33" stretch="1,1">
     <item>
      <widget class="QLabel" name="label_34">
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>28</height>
        </size>
       </property>
       <property name="font">
        <font>
         <family>Microsoft YaHei</family>
        </font>
       </property>
       <property name="text">
        <string>串口号</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignCenter</set>
       </property>
       <property name="margin">
        <number>6</number>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QComboBox" name="comboBoxPort">
       <property name="font">
        <font>
         <family>Microsoft YaHei</family>
        </font>
       </property>
       <property name="currentText">
        <string>自动</string>
       </property>
       <item>
        <property name="text">
         <string>自动</string>
        </property>
       </item>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="Line" name="line">
     <property name="frameShadow">
      <enum>QFrame::Plain</enum>
     </property>
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QLabel" name="label_44">
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>28</height>
      </size>
     </property>
     <property name="font">
      <font>
       <family>Microsoft YaHei</family>
      </font>
     </property>
     <property name="text">
      <string>- 系统设置 -</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
     <property name="margin">
      <number>6</number>
     </property>
    </widget>
   </item>
   <item>
    <layout class="QVBoxLayout" name="verticalLayout_3">
     <item>
      <widget class="QRadioButton" name="Chinese_rb">
       <property name="text">
        <string>中文</string>
       </property>
       <property name="checked">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QRadioButton" name="English_rb">
       <property name="text">
        <string>English</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_35" stretch="1,1">
     <item>
      <widget class="QPushButton" name="btnSetMaxP">
       <property name="font">
        <font>
         <family>Microsoft YaHei</family>
        </font>
       </property>
       <property name="text">
        <string>设置最大输出功率</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QDoubleSpinBox" name="spinBoxMaxP">
       <property name="font">
        <font>
         <family>Microsoft YaHei</family>
        </font>
       </property>
       <property name="alignment">
        <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
       </property>
       <property name="suffix">
        <string> W</string>
       </property>
       <property name="decimals">
        <number>0</number>
       </property>
       <property name="minimum">
        <double>1.000000000000000</double>
       </property>
       <property name="maximum">
        <double>25.000000000000000</double>
       </property>
       <property name="singleStep">
        <double>1.000000000000000</double>
       </property>
       <property name="stepType">
        <enum>QAbstractSpinBox::AdaptiveDecimalStepType</enum>
       </property>
       <property name="value">
        <double>25.000000000000000</double>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_38" stretch="1,1">
     <item>
      <widget class="QPushButton" name="btnCali1">
       <property name="font">
        <font>
         <family>Microsoft YaHei</family>
        </font>
       </property>
       <property name="text">
        <string>触发输出校准 (7V)</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="btnCali2">
       <property name="font">
        <font>
         <family>Microsoft YaHei</family>
        </font>
       </property>
       <property name="text">
        <string>触发输入校准 (20V)</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_4" stretch="1,1">
     <item>
      <widget class="QPushButton" name="btnEnableShortProtect">
       <property name="font">
        <font>
         <family>Microsoft YaHei</family>
        </font>
       </property>
       <property name="text">
        <string>开启短路保护</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="btnDisableShortProtect">
       <property name="font">
        <font>
         <family>Microsoft YaHei</family>
        </font>
       </property>
       <property name="text">
        <string>关闭短路保护</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_37" stretch="1,1">
     <item>
      <widget class="QPushButton" name="btnLockKey">
       <property name="font">
        <font>
         <family>Microsoft YaHei</family>
        </font>
       </property>
       <property name="text">
        <string>锁定设备按键</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="btnUnlockKey">
       <property name="font">
        <font>
         <family>Microsoft YaHei</family>
        </font>
       </property>
       <property name="text">
        <string>解锁设备按键</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_43" stretch="1,1">
     <item>
      <widget class="QLabel" name="label_49">
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>28</height>
        </size>
       </property>
       <property name="font">
        <font>
         <family>Microsoft YaHei</family>
        </font>
       </property>
       <property name="text">
        <string>其它设置</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignCenter</set>
       </property>
       <property name="margin">
        <number>6</number>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QFrame" name="frame_2">
       <property name="frameShape">
        <enum>QFrame::StyledPanel</enum>
       </property>
       <property name="frameShadow">
        <enum>QFrame::Raised</enum>
       </property>
       <layout class="QVBoxLayout" name="verticalLayout_2">
        <property name="spacing">
         <number>0</number>
        </property>
        <property name="leftMargin">
         <number>2</number>
        </property>
        <property name="topMargin">
         <number>2</number>
        </property>
        <property name="rightMargin">
         <number>2</number>
        </property>
        <property name="bottomMargin">
         <number>2</number>
        </property>
        <item>
         <widget class="QCheckBox" name="checkBoxOutputWarn">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>28</height>
           </size>
          </property>
          <property name="toolTip">
           <string>开启输出前显示警告</string>
          </property>
          <property name="text">
           <string>输出开启警告</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QCheckBox" name="checkBoxSetLock">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>28</height>
           </size>
          </property>
          <property name="toolTip">
           <string>输出状态下不允许包括辅助功能在内的设定值修改操作</string>
          </property>
          <property name="text">
           <string>输出时锁定参数</string>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="Line" name="line_2">
     <property name="frameShadow">
      <enum>QFrame::Plain</enum>
     </property>
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_3" stretch="1">
     <property name="spacing">
      <number>6</number>
     </property>
     <property name="bottomMargin">
      <number>0</number>
     </property>
     <item>
      <widget class="QPushButton" name="btnOk">
       <property name="font">
        <font>
         <family>Microsoft YaHei</family>
        </font>
       </property>
       <property name="text">
        <string>保存 / OK</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
