import struct
import time
from dataclasses import dataclass
from enum import IntEnum
from typing import Optional, Callable, Any
from loguru import logger


class CommandList(IntEnum):
    WPX_DEVICE_ID_CMD_ID = 0x0000
    WPX_DEFAULT_DATA_CMD_ID = 0x0001
    WPX_EXT_RESULT_DATA_CMD_ID = 0x0002
    WPX_CMD_ENTER_BOOTLOADER_CMD_ID = 0x0003
    WPX_CMD_ENTER_FASTBOOT_CMD_ID = 0x0004
    WPX_CMD_CHECK_SN_CMD_ID = 0x0005
    WPX_CMD_CHECK_VERSION_CMD_ID = 0x0006
    WPX_CMD_FACTORY_RESET_CMD_ID = 0x0007
    WPX_CMD_START_PROTOCOL_CMD_ID = 0x0008
    WPX_DATA_ASK_PROTOCOL_ID = 0x0009
    WPX_DATA_FSK_PROTOCOL_ID = 0x000A
    WPX_CMD_RESET_DEVICE_CMD_ID = 0x000B
    WPX_CMD_SET_LOAD_TYPE_CMD_ID = 0x000C
    WPX_CMD_SET_PROTOCOL_VOLTAGE_CURRENT_CMD_ID = 0x000D
    WPX_CMD_SET_ONLINE_VOLTAGE_CURRENT_CMD_ID = 0x000E
    WPX_CMD_SET_COMPENSATION_CMD_ID = 0x000F
    WPX_CMD_SET_ONLINE_COMPENSATION_CMD_ID = 0x0010
    WPX_CMD_SET_FOD_COMPENSATION_CMD_ID = 0x0011
    WPX_CMD_SET_TEST_MODE_CMD_ID = 0x0012
    WPX_CMD_SET_QI22_MODE_CMD_ID = 0x0013
    WPX_CMD_SET_ASK_SETTINGS_CMD_ID = 0x0014
    WPX_CMD_SET_EPP_VERSION_CMD_ID = 0x0015
    WPX_CMD_SET_MPP_VERSION_CMD_ID = 0x0016
    WPX_CMD_SET_PRO_PROTOCOL_TYPE_CMD_ID = 0x0017
    WPX_CMD_SWITCH_PROTOCOL_ONLINE_CMD_ID = 0x0018
    WPX_CMD_SEND_DATA_PACKET_CMD_ID = 0x0019
    WPX_CMD_QUERY_TX_DATA_PACKET_CMD_ID = 0x001A
    WPX_CMD_SEND_BATTERY_PACKET_CMD_ID = 0x001B
    WPX_CMD_SEND_EPT_PACKET_CMD_ID = 0x001C
    WPX_CMD_START_AUTH_CMD_ID = 0x001D
    WPX_DATA_AUTH_DATA_CMD_ID = 0x001E
    WPX_CMD_START_PROTOCOL_AUTO_DETECT_CMD_ID = 0x001F
    WPX_DATA_PROTOCOL_AUTO_DETECT_RESULT_CMD_ID = 0x0020


PROTOCOL_FRAME_MAX_SIZE = 265


@dataclass
class FrameHeader:
    sof: int = 0xEE
    data_length: int = 0
    seq: int = 0
    crc8: int = 0


@dataclass
class DataPackage:
    header: FrameHeader
    cmd: int
    data: bytes
    crc16: int = 0


@dataclass
class WPXDefaultData:
    vrect: int = 0
    vol: int = 0
    isence: int = 0
    opmode: int = 0


@dataclass
class WPXVersionData:
    software_version: str = ""
    hardware_version: str = ""
    bootloader_version: str = ""


@dataclass
class WPXSNData:
    sn: str = ""


@dataclass
class WPXExtendedData:
    protocol_type: int = 0  # 协议类型: 0x00=BPP, 0x01=EPP, 0x02=MPP
    signal_strength_percent: int = 0
    nego_power: int = 0
    potential_power: int = 0
    qi_version: int = 0
    manufacturer_id: int = 0
    support_auth: bool = False
    support_duplex: bool = False
    support_outband: bool = False
    support_wpid: bool = False
    nrs_value: int = 0
    device_id: int = 0
    power_limit_reason: int = 0
    ecap_calibration_support: bool = False
    buffer_size: int = 0
    concurrent_streams: int = 0
    g_coil_rx: int = 0
    app_value: int = 0
    uid_value: int = 0


@dataclass
class WPXProtocolAutoDetectResult:
    test_status: int = 0
    current_protocol: int = 0
    bpp_result: int = 0
    epp_result: int = 0
    epp_power: int = 0
    mpp_result: int = 0
    mpp_power: int = 0
    mpp25w_result: int = 0
    apple_result: int = 0
    magsafe15w_result: int = 0
    magsafe25w_result: int = 0
    ppde_result: int = 0
    fc_result: int = 0
    mi_result: int = 0
    huawei_result: int = 0


@dataclass
class WPXAuthData:
    brand: str = ""
    product_model: str = ""
    validity_not_before: str = ""
    validity_not_after: str = ""
    serial_number: str = ""
    subject_public_key: str = ""
    wpc_qi_rsid: str = ""
    signature: str = ""


@dataclass
class WPXFullDefaultData:
    vrect: int = 0
    vout: int = 0
    isence: int = 0
    mode: int = 0
    opfreq: int = 0
    cep: int = 0
    rpp: int = 0
    core_temp: int = 0
    eload_temp: int = 0
    ext_probe_temp: int = 0
    bridge_temp: int = 0
    coil_temp: int = 0
    run_time: int = 0
    wh: int = 0
    mah: int = 0
    power_off_count: int = 0


class WPXProtocol:
    def __init__(self):
        self._seq = 0
        self._data_received_callback: Optional[Callable[[str], None]] = None
        
        # Static data properties
        self.vout_data = 0
        self.vrect_data = 0
        self.isence_data = 0
        self.mode_data = 0
        self.temp_data = 0
        self.opferq_data = 0
        self.cep_data = 0
        self.rpp_data = 0
        self.sn_data = ""
        
        # Full default data instance
        self.full_default_data = WPXFullDefaultData()
        
        # Callbacks
        self._realtime_data_callback: Optional[Callable[[int, int, int, int], None]] = None
        self._full_data_callback: Optional[Callable[[dict], None]] = None
        self._extended_data_callback: Optional[Callable[[WPXExtendedData], None]] = None
        self.software_version = ""
        self.hardware_version = ""
        self.bootloader_version = ""
        
        # Extended data properties
        self.full_default_data = WPXFullDefaultData()
        self.extended_data = WPXExtendedData()
        self.protocol_auto_detect_result = WPXProtocolAutoDetectResult()
        self.auth_data = WPXAuthData()
        self.device_id = 0
        
        # Send data callback
        self.send_data_callback: Optional[Callable[[bytes], None]] = None
        
        # Data output callback for GUI
        self.output_callback: Optional[Callable[[str], None]] = None
        self.is_updating = False
        
        # 实时数据回调，用于绕过插值机制直接更新UI
        self._realtime_data_callback: Optional[Callable[[int, int, int, int], None]] = None

    def set_data_received_callback(self, callback: Callable[[str], None]):
        """Set callback for data received events"""
        self._data_received_callback = callback

    def set_send_data_callback(self, callback: Callable[[bytes], None]):
        """Set callback for sending data"""
        self.send_data_callback = callback

    def set_output_callback(self, callback: Callable[[str], None]):
        """Set callback for output display"""
        self.output_callback = callback

    def set_realtime_data_callback(self, callback: Callable[[int, int, int, int], None]):
        """Set callback for realtime data updates (bypassing interpolation)"""
        self._realtime_data_callback = callback
    
    def set_full_data_callback(self, callback: Callable[[dict], None]):
        """Set callback for full 0x0001 data updates"""
        self._full_data_callback = callback

    def set_extended_data_callback(self, callback: Callable[[WPXExtendedData], None]):
        """Set callback for extended data (0x0002) updates"""
        self._extended_data_callback = callback

    def _parse_legacy_default_data(self, payload: bytes):
        """Parse legacy default data format for backward compatibility"""
        payload_len = len(payload)
        
        if payload_len < 13:  # 最小需要13字节来解析基本数据
            # 减少日志频率，每100次只记录一次
            if not hasattr(self, '_payload_warning_count'):
                self._payload_warning_count = 0
            self._payload_warning_count += 1
            if self._payload_warning_count % 100 == 1:
                logger.warning(f"Payload too short for any parsing: got {payload_len} bytes, need at least 13 (warning #{self._payload_warning_count})")
                logger.debug(f"Payload content: {payload.hex() if payload else 'empty'}")
            return
        
        # 兼容性解析：根据实际payload长度解析数据
        try:
            if payload_len >= 2:
                self.vrect_data = struct.unpack('<H', payload[0:2])[0]
            if payload_len >= 4:
                self.vout_data = struct.unpack('<H', payload[2:4])[0]
            if payload_len >= 6:
                self.isence_data = struct.unpack('<H', payload[4:6])[0]
                # 调试：记录数据更新
                if not hasattr(self, '_data_update_logged'):
                    logger.info(f"WPX protocol data updated: vout={self.vout_data}, isence={self.isence_data}")
                    self._data_update_logged = True
                
                # 调试：记录数据更新次数
                if not hasattr(self, '_data_update_count'):
                    self._data_update_count = 0
                self._data_update_count += 1
                
                if self._data_update_count % 50 == 1:
                    logger.info(f"WPX protocol data update #{self._data_update_count}: vout={self.vout_data}, isence={self.isence_data}")
            if payload_len >= 8:
                self.mode_data = struct.unpack('<H', payload[6:8])[0]
            if payload_len >= 10:
                self.temp_data = struct.unpack('<H', payload[8:10])[0]
            if payload_len >= 12:
                self.opferq_data = struct.unpack('<H', payload[10:12])[0]
            if payload_len >= 13:
                self.cep_data = struct.unpack('<b', payload[12:13])[0]
            if payload_len >= 15:
                rpp_raw_data = struct.unpack('<H', payload[12:14])[0]
                if self.mode_data == 0x01:  # 如果是BPP模式
                    self.rpp_data = (rpp_raw_data / 255) *10000
                elif self.mode_data == 0x02:# 如果是EPP模式
                    self.rpp_data = (rpp_raw_data /30000) * 10000
                else:  # 如果是MPP模式
                    self.rpp_data = rpp_raw_data
                # self.rpp_data = struct.unpack('<H', payload[13:15])[0]
            else:
                # 如果没有rpp_data，设置默认值
                self.rpp_data = 0
            
            # 调用实时数据回调，绕过插值机制
            if self._realtime_data_callback:
                try:
                    self._realtime_data_callback(
                        self.vout_data, self.vrect_data, 
                        self.isence_data, self.mode_data
                    )
                except Exception as e:
                    logger.error(f"Realtime data callback error: {e}")
            
            # 为legacy数据也提供部分完整数据回调
            if self._full_data_callback:
                try:
                    # 只提供legacy格式中可用的数据
                    legacy_data_dict = {
                        'vrect': self.vrect_data / 1000.0,  # mV -> V
                        'vout': self.vout_data / 1000.0,     # mV -> V
                        'isence': self.isence_data / 1000.0, # mA -> A
                        'mode': self.mode_data,
                        'opfreq': (self.opferq_data if hasattr(self, 'opferq_data') else 0),  # 直接是kHz，无需转换
                        'cep': self.cep_data if hasattr(self, 'cep_data') else 0,
                        'rpp': (self.rpp_data if hasattr(self, 'rpp_data') else 0) / 1000.0,  # mW -> W
                        
                        # 对于legacy数据，温度和统计数据使用默认值或不可用标记
                        'core_temp': (self.temp_data if hasattr(self, 'temp_data') else 0),  # 直接°C，无需转换
                        'eload_temp': 0.0,     # legacy中不可用
                        'ext_probe_temp': 0.0, # legacy中不可用
                        'bridge_temp': 0.0,    # legacy中不可用
                        'coil_temp': 0.0,      # legacy中不可用
                        'run_time': 0,         # legacy中不可用
                        'wh': 0.0,             # legacy中不可用
                        'mah': 0,              # legacy中不可用
                        'power_off_count': 0   # legacy中不可用
                    }
                    self._full_data_callback(legacy_data_dict)
                    logger.debug("Legacy data callback executed with partial data")
                except Exception as e:
                    logger.error(f"Legacy full data callback error: {e}")
                
            # 如果payload长度异常，记录一次日志用于调试
            if payload_len != 15 and not hasattr(self, '_length_logged'):
                logger.info(f"Non-standard payload length detected: {payload_len} bytes (expected 15). Using compatibility mode.")
                logger.debug(f"Payload: {payload.hex()}")
                logger.info(f"Parsed data: vrect={self.vrect_data}, vout={self.vout_data}, isence={self.isence_data}")
                self._length_logged = True
                
        except struct.error as e:
            logger.error(f"Struct unpack error with payload length {payload_len}: {e}")
            return

    def _parse_extended_result_data(self, payload: bytes):
        """Parse extended result data according to CSV specification"""
        if len(payload) < 1:
            return
            
        cmd_type = payload[0]
        self.extended_data.protocol_type = cmd_type  # 记录协议类型
        
        try:
            if cmd_type == 0x00:  # BPP
                if len(payload) >= 4:  # 协议标识(1) + 信号强度(1) + 协商功率(2) = 4字节
                    # 信号强度：255对应100%
                    raw_signal = payload[1]
                    self.extended_data.signal_strength_percent = int((raw_signal / 255.0) * 100)
                    self.extended_data.nego_power = struct.unpack('<H', payload[2:4])[0]  # 协商功率值，单位W
                    
            elif cmd_type == 0x01:  # EPP
                if len(payload) >= 14:
                    # 信号强度：255对应100%
                    raw_signal = payload[1]
                    self.extended_data.signal_strength_percent = int((raw_signal / 255.0) * 100)
                    self.extended_data.potential_power = struct.unpack('<H', payload[2:4])[0]  # 潜在功率
                    self.extended_data.nego_power = struct.unpack('<H', payload[4:6])[0]  # 协商功率
                    self.extended_data.qi_version = payload[6]  # Qi协议版本
                    self.extended_data.manufacturer_id = struct.unpack('<H', payload[7:9])[0]  # 制造商ID
                    self.extended_data.support_auth = payload[9] != 0  # 鉴权支持标志
                    self.extended_data.support_duplex = payload[10] != 0  # 双工通信能力
                    self.extended_data.support_outband = payload[11] != 0  # 带外通信支持
                    self.extended_data.support_wpid = payload[12] != 0  # 无线电源ID支持
                    self.extended_data.nrs_value = payload[13]  # NRS功率调整比率
                    
            elif cmd_type == 0x02:  # MPP
                if len(payload) >= 16:
                    # 信号强度：255对应100%
                    raw_signal = payload[1]
                    self.extended_data.signal_strength_percent = int((raw_signal / 255.0) * 100)
                    self.extended_data.potential_power = struct.unpack('<H', payload[2:4])[0]  # 潜在功率
                    self.extended_data.nego_power = struct.unpack('<H', payload[4:6])[0]  # 协商功率
                    self.extended_data.device_id = struct.unpack('<H', payload[6:8])[0]  # 设备ID
                    self.extended_data.power_limit_reason = payload[8]  # 功率限制原因代码
                    self.extended_data.ecap_calibration_support = payload[9] != 0  # eCap校准支持
                    self.extended_data.buffer_size = payload[10]  # 缓冲区大小
                    self.extended_data.concurrent_streams = payload[11]  # 并发数据流数量
                    self.extended_data.g_coil_rx = struct.unpack('<H', payload[12:14])[0]  # 接收线圈ID
                    self.extended_data.app_value = payload[14]  # 应用自定义字段
                    self.extended_data.uid_value = payload[15]  # UID标识字段
                    
            logger.debug(f"Extended data parsed for type {cmd_type:02X}")
            
            # 调用扩展数据回调
            if self._extended_data_callback:
                try:
                    self._extended_data_callback(self.extended_data)
                except Exception as e:
                    logger.error(f"Error in extended data callback: {e}")
            
        except struct.error as e:
            logger.error(f"Extended data parse error: {e}")

    def _parse_auth_data(self, payload: bytes):
        """Parse authentication data with sub-packets"""
        if len(payload) < 3:
            return
            
        offset = 0
        while offset < len(payload):
            if offset + 3 > len(payload):
                break
                
            if payload[offset] != 0xEF:
                break
                
            sub_length = struct.unpack('<H', payload[offset+1:offset+3])[0]
            if offset + 3 + sub_length > len(payload):
                break
                
            sub_data = payload[offset+3:offset+3+sub_length]
            
            # Determine sub-packet type based on order (as per CSV specification)
            if not hasattr(self, '_auth_packet_count'):
                self._auth_packet_count = 0
            
            if self._auth_packet_count == 0:
                self.auth_data.brand = sub_data.decode('utf-8', errors='ignore')
            elif self._auth_packet_count == 1:
                self.auth_data.product_model = sub_data.decode('utf-8', errors='ignore')
            elif self._auth_packet_count == 2:
                self.auth_data.validity_not_before = sub_data.decode('utf-8', errors='ignore')
            elif self._auth_packet_count == 3:
                self.auth_data.validity_not_after = sub_data.decode('utf-8', errors='ignore')
            elif self._auth_packet_count == 4:
                self.auth_data.serial_number = sub_data.decode('utf-8', errors='ignore')
            elif self._auth_packet_count == 5:
                self.auth_data.subject_public_key = sub_data.hex()
            elif self._auth_packet_count == 6:
                self.auth_data.wpc_qi_rsid = sub_data.decode('utf-8', errors='ignore')
            elif self._auth_packet_count == 7:
                self.auth_data.signature = sub_data.hex()
                
            self._auth_packet_count += 1
            offset += 3 + sub_length
            
        logger.info(f"Authentication data parsed: {self.auth_data}")

    def _parse_protocol_auto_detect_result(self, payload: bytes):
        """Parse protocol auto-detect result data"""
        if len(payload) < 17:
            return
            
        try:
            self.protocol_auto_detect_result.test_status = payload[0]
            self.protocol_auto_detect_result.current_protocol = payload[1]
            self.protocol_auto_detect_result.bpp_result = payload[2]
            self.protocol_auto_detect_result.epp_result = payload[3]
            self.protocol_auto_detect_result.epp_power = struct.unpack('<H', payload[4:6])[0]
            self.protocol_auto_detect_result.mpp_result = payload[6]
            self.protocol_auto_detect_result.mpp_power = struct.unpack('<H', payload[7:9])[0]
            self.protocol_auto_detect_result.mpp25w_result = payload[9]
            self.protocol_auto_detect_result.apple_result = payload[10]
            self.protocol_auto_detect_result.magsafe15w_result = payload[11]
            self.protocol_auto_detect_result.magsafe25w_result = payload[12]
            self.protocol_auto_detect_result.ppde_result = payload[13]
            self.protocol_auto_detect_result.fc_result = payload[14]
            self.protocol_auto_detect_result.mi_result = payload[15]
            self.protocol_auto_detect_result.huawei_result = payload[16]
            
            logger.info(f"Protocol auto-detect result: {self.protocol_auto_detect_result}")
            
        except struct.error as e:
            logger.error(f"Protocol auto-detect result parse error: {e}")

    def safe_array_copy(self, data: bytes, data_length: int) -> bytes:
        """Safely copy payload data from received packet"""
        if not data or data_length < 2:
            logger.warning("Invalid data parameters")
            return b''
        
        if len(data) < 7 + data_length - 2:
            logger.warning("Data length insufficient")
            return b''
        
        try:
            # Copy data starting from position 7, excluding 2 bytes for CMD
            return data[7:7 + data_length - 2]
        except Exception as e:
            logger.error(f"Error copying data: {e}")
            return b''

    def process_received_data(self, data: bytes):
        """Process received data packet"""
        if not data or len(data) <= 0:
            return
        
        head = data[0]
        if head != 0xEE:
            # 在Ymodem传输期间，可能会收到其他协议的数据包，这是正常的
            # 只记录调试信息而不是警告，避免日志中出现大量错误信息
            if head == 0x06:  # ACK字符，Ymodem协议中的正常响应
                logger.debug(f"Received ACK during potential Ymodem transfer: {head:02X}")
            elif head in [0x15, 0x18, 0x43]:  # NAK, CAN, 'C' - Ymodem协议字符
                logger.debug(f"Received Ymodem protocol byte: {head:02X}")
            else:
                logger.warning(f"Unexpected head byte: {head:02X}")
            return
        
        # Get data length (little-endian)
        if len(data) < 5:
            logger.warning("Packet too short")
            return
            
        data_length = (data[2] << 8) | data[1]
        
        # Extract other fields
        seq = data[3]
        crc8 = data[4]
        
        if len(data) < 7:
            logger.warning("Packet too short for CMD")
            return
            
        cmd = (data[6] << 8) | data[5]
        
        # Extract payload
        payload = self.safe_array_copy(data, data_length)
        
        logger.debug(f"Received - Length: {data_length}, Seq: {seq}, CRC8: {crc8:02X}, CMD: {cmd:04X}")
        
        # Process the payload
        self._process_payload(cmd, payload)

    def _process_payload(self, cmd: int, payload: bytes):
        """Process payload data based on command"""
        logger.debug(f"Processing CMD: {cmd:04X}, Payload Length: {len(payload)}")
        
        # 使用datetime获取带毫秒的时间戳
        import datetime
        now = datetime.datetime.now().strftime("%H:%M:%S.%f")[:-3]
        
        if cmd == CommandList.WPX_DEFAULT_DATA_CMD_ID:
            # Parse full default data according to CSV specification (33 bytes total)
            payload_len = len(payload)
            
            # 添加详细的数据长度日志
            if payload_len == 33:
                logger.info(f"Received full 33-byte 0x0001 data - using complete parsing")
            elif payload_len == 15:
                logger.info(f"Received legacy 15-byte 0x0001 data - using compatibility parsing")
            else:
                logger.warning(f"Received unexpected 0x0001 data length: {payload_len} bytes")
            
            if payload_len < 33:
                # For backward compatibility, use the original parsing
                self._parse_legacy_default_data(payload)
                return
            
            # Full default data parsing (33 bytes)
            try:
                self.full_default_data.vrect = struct.unpack('<H', payload[0:2])[0]
                self.full_default_data.vout = struct.unpack('<H', payload[2:4])[0]
                self.full_default_data.isence = struct.unpack('<H', payload[4:6])[0]
                self.full_default_data.mode = struct.unpack('<H', payload[6:8])[0]
                self.full_default_data.opfreq = struct.unpack('<H', payload[8:10])[0]
                self.full_default_data.cep = struct.unpack('<b', payload[10:11])[0]

                rpp_raw_data = struct.unpack('<H', payload[11:13])[0]
                if self.full_default_data.mode == 0x01:  # 如果是BPP模式
                    self.full_default_data.rpp = (rpp_raw_data / 255) *10000
                elif self.full_default_data.mode == 0x02:# 如果是EPP模式
                    print(f"Negotiated power: {rpp_raw_data}, Extended nego power: {(rpp_raw_data / (0xFFFF)*30)}")
                    if self.extended_data.nego_power > 0:
                        self.full_default_data.rpp = ((rpp_raw_data / (0xFFFF)) * self.extended_data.nego_power) * 1000
                    else:
                        self.full_default_data.rpp = 0 #(rpp_raw_data / (self.extended_data.nego_power * 2)) * 10000
                else:  # 如果是MPP模式
                    self.full_default_data.rpp = rpp_raw_data

                # self.full_default_data.rpp = struct.unpack('<H', payload[11:13])[0]
                
                # Temperature data
                self.full_default_data.core_temp = struct.unpack('<H', payload[13:15])[0]
                self.full_default_data.eload_temp = struct.unpack('<H', payload[15:17])[0]
                self.full_default_data.ext_probe_temp = struct.unpack('<H', payload[17:19])[0]
                self.full_default_data.bridge_temp = struct.unpack('<H', payload[19:21])[0]
                self.full_default_data.coil_temp = struct.unpack('<H', payload[21:23])[0]
                
                # Runtime and energy data
                self.full_default_data.run_time = struct.unpack('<I', payload[23:27])[0]
                self.full_default_data.wh = struct.unpack('<H', payload[27:29])[0]
                self.full_default_data.mah = struct.unpack('<H', payload[29:31])[0]
                self.full_default_data.power_off_count = struct.unpack('<H', payload[31:33])[0]
                
                # Update legacy data for backward compatibility
                self.vrect_data = self.full_default_data.vrect
                self.vout_data = self.full_default_data.vout
                self.isence_data = self.full_default_data.isence
                self.mode_data = self.full_default_data.mode
                self.opferq_data = self.full_default_data.opfreq
                self.cep_data = self.full_default_data.cep
                self.rpp_data = self.full_default_data.rpp
                self.temp_data = self.full_default_data.core_temp
                
                # Call realtime data callback
                if self._realtime_data_callback:
                    try:
                        self._realtime_data_callback(
                            self.vout_data, self.vrect_data, 
                            self.isence_data, self.mode_data
                        )
                    except Exception as e:
                        logger.error(f"Realtime data callback error: {e}")
                
                # Call full data callback with all parsed data
                if self._full_data_callback:
                    try:
                        full_data_dict = {
                            # Electrical parameters - Converting from mV/mA to V/A for display
                            'vrect': self.full_default_data.vrect / 1000.0,  # mV -> V
                            'vout': self.full_default_data.vout / 1000.0,     # mV -> V  
                            'isence': self.full_default_data.isence / 1000.0, # mA -> A
                            'mode': self.full_default_data.mode,
                            'opfreq': self.full_default_data.opfreq,  # 直接是kHz，无需转换
                            'cep': self.full_default_data.cep,
                            'rpp': self.full_default_data.rpp / 1000.0,       # mW -> W
                            
                            # Temperature data - 直接是°C，无需转换
                            'core_temp': self.full_default_data.core_temp,     # 直接°C
                            'eload_temp': self.full_default_data.eload_temp,   # 直接°C
                            'ext_probe_temp': self.full_default_data.ext_probe_temp, # 直接°C
                            'bridge_temp': self.full_default_data.bridge_temp,  # 直接°C
                            'coil_temp': self.full_default_data.coil_temp,     # 直接°C
                            
                            # Runtime and energy data
                            'run_time': self.full_default_data.run_time,    # seconds
                            'wh': self.full_default_data.wh / 1000.0,       # mWh -> Wh
                            'mah': self.full_default_data.mah,              # mAh
                            'power_off_count': self.full_default_data.power_off_count
                        }
                        self._full_data_callback(full_data_dict)
                    except Exception as e:
                        logger.error(f"Full data callback error: {e}")
                        
            except struct.error as e:
                logger.error(f"Struct unpack error with payload length {payload_len}: {e}")
                return
            
        elif cmd == CommandList.WPX_CMD_CHECK_SN_CMD_ID:
            # 根据WPX命令码文档，SN响应是14字节ASCII编码
            if len(payload) < 14:
                logger.warning(f"Insufficient payload length for SN data: got {len(payload)} bytes, need 14")
                self.sn_data = ""
                return
            
            # 解析14字节的序列号，使用更严格的乱码检测
            try:
                # 提取14字节数据
                sn_bytes = payload[:14]
                
                # 记录原始字节用于调试乱码问题
                ascii_repr = [chr(b) if 32 <= b <= 126 else f'\\x{b:02x}' for b in sn_bytes]
                logger.debug(f"Raw SN bytes: {sn_bytes.hex()} -> ASCII: {ascii_repr}")
                
                # 严格的数据验证和过滤
                valid_chars = []
                consecutive_invalid = 0
                
                for i, byte in enumerate(sn_bytes):
                    if byte == 0:  # NULL终止符，停止解析
                        break
                    elif 32 <= byte <= 126:  # 可打印ASCII字符
                        # 进一步过滤明显的乱码字符
                        char = chr(byte)
                        if char in '!"#$%&\'()*+,./:;<=>?@[\\]^`{|}~':  # 通常SN不包含的特殊字符
                            consecutive_invalid += 1
                            if consecutive_invalid <= 2:  # 允许少量特殊字符（如连字符）
                                valid_chars.append(char)
                        else:
                            consecutive_invalid = 0
                            valid_chars.append(char)
                    else:
                        consecutive_invalid += 1
                        # 如果连续出现太多无效字符，很可能是乱码
                        if consecutive_invalid > 3:
                            logger.warning(f"Too many consecutive invalid bytes detected at position {i}, likely corrupted data")
                            break
                
                # 转换为字符串并清理
                candidate_sn = ''.join(valid_chars).strip()
                
                # 更严格的验证逻辑
                if len(candidate_sn) < 3:
                    logger.warning(f"SN data too short: '{candidate_sn}' (raw: {sn_bytes.hex()})")
                    self.sn_data = "数据过短"
                elif len(candidate_sn) > 14:
                    logger.warning(f"SN data too long: '{candidate_sn}' (raw: {sn_bytes.hex()})")
                    self.sn_data = "数据过长"
                elif consecutive_invalid > 5:  # 如果无效字符太多
                    logger.warning(f"Too many invalid characters in SN: '{candidate_sn}' (raw: {sn_bytes.hex()})")
                    self.sn_data = "乱码检测"
                else:
                    # 进一步检查是否是明显的乱码模式
                    # 检查是否包含过多的重复字符或特殊模式
                    unique_chars = set(candidate_sn)
                    if len(unique_chars) == 1 and len(candidate_sn) > 3:  # 全是相同字符
                        logger.warning(f"SN contains only repeated characters: '{candidate_sn}' (raw: {sn_bytes.hex()})")
                        self.sn_data = "重复字符"
                    elif candidate_sn.count('?') > 2:  # 包含太多问号
                        logger.warning(f"SN contains too many question marks: '{candidate_sn}' (raw: {sn_bytes.hex()})")
                        self.sn_data = "包含乱码"
                    else:
                        # 数据看起来合理
                        self.sn_data = candidate_sn
                        logger.info(f"Valid SN received: {self.sn_data}")
                    
            except Exception as e:
                logger.error(f"Error parsing SN data: {e}, raw bytes: {payload[:14].hex()}")
                self.sn_data = "解析错误"
            
        elif cmd == CommandList.WPX_CMD_CHECK_VERSION_CMD_ID:
            if len(payload) < 24:
                logger.warning(f"Insufficient payload length for version data: got {len(payload)} bytes, need 24")
                return
            
            # 根据 device_wpx_version_data_t 结构体解析版本信息：
            # APP版本号: 0-7字节 (8字节)
            # Bootloader版本号: 8-15字节 (8字节)
            # 硬件版本号: 16-23字节 (8字节)
            
            try:
                # 提取原始字节数据
                app_bytes = payload[0:8]
                bootloader_bytes = payload[8:16]
                hardware_bytes = payload[16:24]
                
                # 使用严格的版本数据验证函数
                self.software_version = self._validate_version_data(app_bytes, "APP")
                self.bootloader_version = self._validate_version_data(bootloader_bytes, "Bootloader")
                self.hardware_version = self._validate_version_data(hardware_bytes, "Hardware")
                
                logger.info(f"App Version: {self.software_version}")
                logger.info(f"Bootloader Version: {self.bootloader_version}")
                logger.info(f"Hardware Version: {self.hardware_version}")
                
            except Exception as e:
                logger.error(f"Error parsing version data: {e}")
                self.software_version = "解析错误"
                self.bootloader_version = "解析错误"
                self.hardware_version = "解析错误"
            
        elif cmd == CommandList.WPX_DATA_ASK_PROTOCOL_ID:
            if self.output_callback and self.is_updating:
                hex_output = f"{now}-> "
                payload_len = len(payload) - 2 if len(payload) > 2 else len(payload)
                for i in range(payload_len):
                    hex_output += f"{payload[i]:02X} "
                self.output_callback(hex_output)
                
        elif cmd == CommandList.WPX_DATA_FSK_PROTOCOL_ID:
            if self.output_callback and self.is_updating:
                hex_output = f"{now}<- "
                payload_len = len(payload) - 2 if len(payload) > 2 else len(payload)
                if payload_len > 0:
                    for i in range(payload_len):
                        hex_output += f"{payload[i]:02X} "
                else:
                    hex_output += "删除末尾 2 个字节后数据过短显示"
                self.output_callback(hex_output)
                
        elif cmd == CommandList.WPX_DEVICE_ID_CMD_ID:
            if len(payload) >= 4:
                self.device_id = struct.unpack('<I', payload[0:4])[0]
                logger.info(f"Device ID: {self.device_id}")
            
        elif cmd == CommandList.WPX_EXT_RESULT_DATA_CMD_ID:
            self._parse_extended_result_data(payload)
            
        elif cmd == CommandList.WPX_DATA_AUTH_DATA_CMD_ID:
            self._parse_auth_data(payload)
            
        elif cmd == CommandList.WPX_DATA_PROTOCOL_AUTO_DETECT_RESULT_CMD_ID:
            self._parse_protocol_auto_detect_result(payload)
            
        else:
            logger.warning(f"Unknown command: {cmd:04X}")

    def _pack_header(self, data_length: int, cmd_id: int) -> FrameHeader:
        """Pack frame header"""
        self._seq = (self._seq + 1) % 256
        
        header = FrameHeader(
            sof=0xEE,
            data_length=data_length,
            seq=self._seq,
            crc8=10  # Simplified CRC8, should implement proper calculation
        )
        return header

    def _struct_to_bytes(self, package: DataPackage) -> bytes:
        """Convert DataPackage to bytes"""
        data = bytearray()
        
        # Pack header
        data.append(package.header.sof)
        data.extend(struct.pack('<H', package.header.data_length))
        data.append(package.header.seq)
        data.append(package.header.crc8)
        
        # Pack CMD (little-endian)
        data.extend(struct.pack('<H', package.cmd))
        
        # Pack data
        if package.data:
            data.extend(package.data)
        
        # Pack CRC16 (big-endian for compatibility)
        data.extend(struct.pack('>H', package.crc16))
        
        return bytes(data)

    def _send_command(self, cmd_id: int, data: bytes = b'\x00'):
        """Send command with data"""
        package = DataPackage(
            header=self._pack_header(len(data), cmd_id),
            cmd=cmd_id,
            data=data,
            crc16=0
        )
        
        byte_array = self._struct_to_bytes(package)
        logger.debug(f"Sending command {cmd_id:04X}: {byte_array.hex()}")
        
        if self.send_data_callback:
            self.send_data_callback(byte_array)

    def enter_bootloader_cmd(self):
        """Send enter bootloader command"""
        self._send_command(CommandList.WPX_CMD_ENTER_BOOTLOADER_CMD_ID)

    def enter_fastboot_cmd(self):
        """Send enter Fastboot command"""
        self._send_command(CommandList.WPX_CMD_ENTER_FASTBOOT_CMD_ID, b'\x01')

    def check_sn_cmd(self):
        """Send check SN command"""
        self._send_command(CommandList.WPX_CMD_CHECK_SN_CMD_ID)

    def check_version_cmd(self):
        """Send check version command"""
        self._send_command(CommandList.WPX_CMD_CHECK_VERSION_CMD_ID)

    def factory_reset_cmd(self):
        """Send factory reset command"""
        self._send_command(CommandList.WPX_CMD_FACTORY_RESET_CMD_ID)

    def start_protocol_cmd(self):
        """Send start protocol command"""
        self._send_command(CommandList.WPX_CMD_START_PROTOCOL_CMD_ID)

    def set_device_id_cmd(self, device_id: int):
        """Set device ID command"""
        data = struct.pack('<I', device_id)
        self._send_command(CommandList.WPX_DEVICE_ID_CMD_ID, data)

    def query_device_id_cmd(self):
        """Query device ID command"""
        self._send_command(CommandList.WPX_DEVICE_ID_CMD_ID, b'\x00')

    def reset_device_cmd(self):
        """Reset device command"""
        self._send_command(CommandList.WPX_CMD_RESET_DEVICE_CMD_ID)

    def set_load_type_cmd(self, load_type: int):
        """Set load type command (0x00: system load, 0x01: external load)"""
        data = struct.pack('<B', load_type)
        self._send_command(CommandList.WPX_CMD_SET_LOAD_TYPE_CMD_ID, data)

    def set_protocol_voltage_current_cmd(self, protocol_type: int, manual_mode: bool, voltage: int, current: int):
        """Set protocol voltage/current command"""
        mode = 0x01 if manual_mode else 0x00
        data = struct.pack('<BBHH', protocol_type, mode, voltage, current)
        self._send_command(CommandList.WPX_CMD_SET_PROTOCOL_VOLTAGE_CURRENT_CMD_ID, data)

    def set_online_voltage_current_cmd(self, control_mode: int, voltage: int, current: int):
        """Set online voltage/current command (not saved)"""
        data = struct.pack('<BHH', control_mode, voltage, current)
        self._send_command(CommandList.WPX_CMD_SET_ONLINE_VOLTAGE_CURRENT_CMD_ID, data)

    def set_compensation_cmd(self, protocol_type: int, manual_mode: bool, voltage: int, current: int, frequency: int):
        """Set compensation voltage/current/frequency command"""
        mode = 0x01 if manual_mode else 0x00
        data = struct.pack('<BBhhh', protocol_type, mode, voltage, current, frequency)
        self._send_command(CommandList.WPX_CMD_SET_COMPENSATION_CMD_ID, data)

    def set_online_compensation_cmd(self, voltage: int, current: int, frequency: int):
        """Set online compensation values command"""
        data = struct.pack('<hhh', voltage, current, frequency)
        self._send_command(CommandList.WPX_CMD_SET_ONLINE_COMPENSATION_CMD_ID, data)

    def set_fod_compensation_cmd(self, rpp_comp: int, vrect_comp: int, irect_comp: int, prect_comp: int):
        """Set FOD compensation command"""
        data = struct.pack('<hhhh', rpp_comp, vrect_comp, irect_comp, prect_comp)
        self._send_command(CommandList.WPX_CMD_SET_FOD_COMPENSATION_CMD_ID, data)

    def set_test_mode_cmd(self, mode: int):
        """Set test mode command (0x01: normal, 0x02: fast, 0x03: compatible)"""
        data = struct.pack('<B', mode)
        self._send_command(CommandList.WPX_CMD_SET_TEST_MODE_CMD_ID, data)

    def set_qi22_mode_cmd(self, mode: int):
        """Set Qi2.2 mode command (0x00: force mode, 0x01: standard mode)"""
        data = struct.pack('<B', mode)
        self._send_command(CommandList.WPX_CMD_SET_QI22_MODE_CMD_ID, data)

    def set_ask_settings_cmd(self, channel: int, depth: int):
        """Set ASK settings command"""
        data = struct.pack('<BB', channel, depth)
        self._send_command(CommandList.WPX_CMD_SET_ASK_SETTINGS_CMD_ID, data)

    def set_epp_version_cmd(self, version: int):
        """Set EPP version command (0x00: Qi1.2, 0x01: Qi1.3, 0x02: Qi2.0)"""
        data = struct.pack('<B', version)
        self._send_command(CommandList.WPX_CMD_SET_EPP_VERSION_CMD_ID, data)

    def set_mpp_version_cmd(self, version: int):
        """Set MPP version command (0x00: Qi2.0, 0x01: Qi2.1, 0x02: Qi2.2)"""
        data = struct.pack('<B', version)
        self._send_command(CommandList.WPX_CMD_SET_MPP_VERSION_CMD_ID, data)

    def set_pro_protocol_type_cmd(self, protocol_type: int):
        """Set pro protocol type command"""
        data = struct.pack('<B', protocol_type)
        self._send_command(CommandList.WPX_CMD_SET_PRO_PROTOCOL_TYPE_CMD_ID, data)

    def switch_protocol_online_cmd(self, protocol: int):
        """Switch protocol online command"""
        data = struct.pack('<B', protocol)
        self._send_command(CommandList.WPX_CMD_SWITCH_PROTOCOL_ONLINE_CMD_ID, data)

    def send_data_packet_cmd(self, packet_data: bytes):
        """Send data packet command (up to 32 bytes)"""
        if len(packet_data) > 32:
            logger.warning("Data packet too long, truncating to 32 bytes")
            packet_data = packet_data[:32]
        self._send_command(CommandList.WPX_CMD_SEND_DATA_PACKET_CMD_ID, packet_data)

    def query_tx_data_packet_cmd(self):
        """Query TX data packet command"""
        self._send_command(CommandList.WPX_CMD_QUERY_TX_DATA_PACKET_CMD_ID)

    def send_battery_packet_cmd(self, battery_level: int):
        """Send battery packet command (0-100%)"""
        data = struct.pack('<B', battery_level)
        self._send_command(CommandList.WPX_CMD_SEND_BATTERY_PACKET_CMD_ID, data)

    def send_ept_packet_cmd(self, ept_code: int):
        """Send EPT (End Power Transfer) packet command"""
        data = struct.pack('<B', ept_code)
        self._send_command(CommandList.WPX_CMD_SEND_EPT_PACKET_CMD_ID, data)

    def start_auth_cmd(self, auth_type: int):
        """Start authentication command (0x00: EPP1.3, 0x01: MPP)"""
        data = struct.pack('<B', auth_type)
        self._send_command(CommandList.WPX_CMD_START_AUTH_CMD_ID, data)

    def start_protocol_auto_detect_cmd(self, enable: bool):
        """Start protocol auto-detect command"""
        data = struct.pack('<B', 0x01 if enable else 0x00)
        self._send_command(CommandList.WPX_CMD_START_PROTOCOL_AUTO_DETECT_CMD_ID, data)

    def get_default_data(self) -> WPXDefaultData:
        """Get the latest default data"""
        return WPXDefaultData(
            vrect=self.vrect_data,
            vol=self.vout_data,
            isence=self.isence_data,
            opmode=self.mode_data
        )

    def get_version_data(self) -> WPXVersionData:
        """Get version information"""
        return WPXVersionData(
            software_version=self.software_version,
            hardware_version=self.hardware_version,
            bootloader_version=self.bootloader_version
        )

    def get_sn_data(self) -> WPXSNData:
        """Get serial number data"""
        return WPXSNData(sn=self.sn_data)

    def get_full_default_data(self) -> WPXFullDefaultData:
        """Get full default data"""
        return self.full_default_data

    def get_extended_data(self) -> WPXExtendedData:
        """Get extended result data"""
        return self.extended_data

    def get_auth_data(self) -> WPXAuthData:
        """Get authentication data"""
        return self.auth_data

    def get_protocol_auto_detect_result(self) -> WPXProtocolAutoDetectResult:
        """Get protocol auto-detect result"""
        return self.protocol_auto_detect_result

    def get_device_id(self) -> int:
        """Get device ID"""
        return self.device_id

    def _validate_version_data(self, version_bytes: bytes, field_name: str) -> str:
        """验证版本数据，检测并处理乱码"""
        try:
            # 记录原始字节用于调试
            ascii_repr = [chr(b) if 32 <= b <= 126 else f'\\x{b:02x}' for b in version_bytes]
            logger.debug(f"Raw {field_name} version bytes: {version_bytes.hex()} -> ASCII: {ascii_repr}")
            
            # 严格的版本数据验证和过滤
            valid_chars = []
            consecutive_invalid = 0
            
            for i, byte in enumerate(version_bytes):
                if byte == 0:  # NULL终止符，停止解析
                    break
                elif 32 <= byte <= 126:  # 可打印ASCII字符
                    char = chr(byte)
                    # 版本号通常包含数字、字母、点号、连字符、下划线
                    if char.isalnum() or char in '.-_':
                        consecutive_invalid = 0
                        valid_chars.append(char)
                    elif char in ' /\\()[]{}':  # 某些可能的版本格式字符
                        consecutive_invalid += 1
                        if consecutive_invalid <= 2:  # 允许少量特殊字符
                            valid_chars.append(char)
                    else:
                        consecutive_invalid += 1
                        # 如果连续无效字符太多，可能是乱码
                        if consecutive_invalid > 3:
                            logger.warning(f"Too many consecutive invalid characters in {field_name} version at position {i}")
                            break
                else:
                    consecutive_invalid += 1
                    if consecutive_invalid > 3:
                        logger.warning(f"Too many consecutive non-ASCII bytes in {field_name} version at position {i}")
                        break
            
            # 转换为字符串并清理
            candidate_version = ''.join(valid_chars).strip()
            
            # 版本号验证逻辑
            if not candidate_version:
                logger.warning(f"{field_name} version is empty (raw: {version_bytes.hex()})")
                return "空版本"
            elif len(candidate_version) > 8:
                logger.warning(f"{field_name} version too long: '{candidate_version}' (raw: {version_bytes.hex()})")
                return "版本过长"
            elif consecutive_invalid > 4:
                logger.warning(f"Too many invalid characters in {field_name} version: '{candidate_version}' (raw: {version_bytes.hex()})")
                return "版本乱码"
            else:
                # 进一步检查版本格式的合理性
                unique_chars = set(candidate_version)
                if len(unique_chars) == 1 and len(candidate_version) > 2:  # 全是相同字符
                    logger.warning(f"{field_name} version contains only repeated characters: '{candidate_version}' (raw: {version_bytes.hex()})")
                    return "重复字符"
                elif candidate_version.count('?') > 1:  # 包含问号
                    logger.warning(f"{field_name} version contains question marks: '{candidate_version}' (raw: {version_bytes.hex()})")
                    return "包含乱码"
                else:
                    # 版本数据看起来合理
                    logger.debug(f"Valid {field_name} version: {candidate_version}")
                    return candidate_version
                    
        except Exception as e:
            logger.error(f"Error validating {field_name} version data: {e}, raw bytes: {version_bytes.hex()}")
            return "验证错误"
        