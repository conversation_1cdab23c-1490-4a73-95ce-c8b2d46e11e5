﻿通信方向,命令码,功能模块,数据帧格式,数据长度,配置存储,测量单位,技术规范与功能描述,
双向通信,0x0000,设备身份管理,"请求帧：操作类型(1 Byte) + 设备ID(4 Bytes,仅设置操作时包含)
响应帧：当前设备ID(4 Bytes,32位无符号整数)
操作类型：0x00=查询当前设备号，0x01=设置新设备号","请求：1-5 Bytes
响应：4 Bytes",YES,无单位,设备唯一标识符管理模块，支持动态配置设备ID。用于设备身份识别、网络管理和设备追踪。设置操作将永久存储至非易失性存储器。,
WPX→PC,0x0001,实时遥测数据流,"电气参数组(小端序列化)：
• Vrect整流电压(2 Bytes,0-1)
• Vout输出电压(2 Bytes,2-3) 
• Isence检测电流(2 Bytes,4-5)
• 工作模式标识(2 Bytes,6-7)
• 工作频率(2 Bytes,8-9)
• CEP控制错误包计数器(1 Byte,10)
• 接收功率RPP(2 Bytes,11-12)

温度监控矩阵：
• 内核温度(2 Bytes,13-14)
• 负载温度(2 Bytes,15-16) 
• 外部探头温度(2 Bytes,17-18)
• 整流桥温度(2 Bytes,19-20)
• 线圈温度(2 Bytes,21-22)

运行统计数据：
• 设备累计运行时间(4 Bytes,23-26,秒级精度)
• 累计充电瓦时(2 Bytes,27-28)
• 累计充电毫安时(2 Bytes,29-30)
• 系统掉电次数计数器(2 Bytes,31-32)","固定33 Bytes
更新频率：30Hz",NO,mV/mA/kHz/°C/S/Wh/mAh,高频实时遥测数据流，以30Hz频率自动上报设备完整运行状态。包含电气参数监控、多点温度采集、运行统计分析等关键性能指标。数据采用小端字节序编码，确保跨平台兼容性。,
WPX→PC,0x0002,无线充电协议测试数据,"协议标识字段(1 Byte)：
• 0x00=BPP基础功率协议
• 0x01=EPP扩展功率协议  
• 0x02=MPP磁吸功率协议

BPP协议数据结构(总长度2 Bytes)：
• 信号强度百分比(1 Byte,范围0-100%)
• 协商功率值(2 Bytes,单位：W)

EPP协议数据结构(总长度13 Bytes)：
• 信号强度百分比(1 Byte,范围0-100%)
• 潜在功率(2 Bytes,分辨率0.5W)
• 协商功率(2 Bytes,分辨率0.5W) 
• Qi协议版本(1 Byte,BCD编码格式)
• 制造商ID(2 Bytes,16位标识符)
• 鉴权支持标志(1 Byte,0=不支持,1=支持)
• 双工通信能力(1 Byte,0=单工,1=双工)
• 带外通信支持(1 Byte,0=不支持,1=支持)
• 无线电源ID支持(1 Byte,0=不支持,1=支持)
• NRS功率调整比率(1 Byte,百分比表示)

MPP协议数据结构(总长度15 Bytes)：
• 信号强度百分比(1 Byte,范围0-100%)
• 潜在功率(2 Bytes,分辨率0.1W)
• 协商功率(2 Bytes,分辨率0.1W)
• 设备ID(2 Bytes,16位标识符)
• 功率限制原因代码(1 Byte)
• eCap校准支持(1 Byte,0=不支持,1=支持)
• 缓冲区大小(1 Byte,字节数)
• 并发数据流数量(1 Byte)
• 接收线圈ID(2 Bytes,16位标识符)
• 应用自定义字段(1 Byte)
• UID标识字段(1 Byte)","变长数据帧
BPP：3 Bytes
EPP：14 Bytes  
MPP：16 Bytes
更新频率：1Hz",NO,百分比/瓦特,无线充电协议兼容性测试模块，实时监控和上报BPP/EPP/MPP三大主流协议的握手过程、功率协商结果和设备能力参数。支持WPC标准规范，提供详细的协议分析数据用于兼容性验证和性能评估。,
PC→WPX,0x0003,应用固件更新引导,无命令参数,命令：0 Bytes,NO,无单位,系统固件更新模块-应用层。执行后设备立即进入Bootloader引导模式，准备接收新的应用程序固件。更新过程中设备将重启并暂停所有业务功能，直至固件更新完成。适用于功能升级、Bug修复和性能优化。,
PC→WPX,0x0004,引导程序更新模式,无命令参数,命令：0 Bytes,NO,无单位,系统固件更新模块-引导层。执行后设备进入Fastboot快速启动模式，用于更新底层Bootloader程序。此操作属于系统级维护，涉及启动引导程序修改，操作风险较高，建议仅在必要时由专业人员执行。,
PC→WPX,0x0005,设备序列号查询,无命令参数,"请求：0 Bytes
响应：14 Bytes",NO,ASCII字符编码,设备唯一序列号查询接口。返回14字节ASCII编码的设备序列号，用于设备唯一性识别、生产追溯、质量管理和售后服务。序列号在设备生产时写入，终身不变，可作为设备身份认证的重要依据。,
PC→WPX,0x0006,固件版本信息查询,无命令参数,"请求：0 Bytes
响应：32 Bytes
• 应用软件版本(8 Bytes,0-7)
• 引导程序版本(8 Bytes,8-15) 
• 系统保留字段(16 Bytes,16-31)","请求：0 Bytes
响应：32 Bytes",✔,版本号字符串,系统固件版本信息查询模块。返回详细的软件版本信息，包括应用程序版本、引导程序版本等关键组件版本号。用于软件兼容性检查、故障诊断、版本管理和技术支持。保留字段用于未来功能扩展。
PC→WPX,0x0007,系统恢复出厂设置,无命令参数,命令：0 Bytes,NO,无单位,系统配置重置模块。将设备所有用户配置参数、校准数据、统计信息等恢复至出厂默认状态。操作将清除非易失性存储器中的所有用户数据，该过程不可逆转。建议在设备故障排查、重新部署或设备回收前执行。,
PC→WPX,0x0008,协议数据流监控启动,无命令参数,命令：0 Bytes,NO,无单位,无线充电协议数据流实时监控模块启动接口。激活ASK/FSK调制数据的实时捕获和传输功能。警告：此功能主要用于协议分析和调试目的，在正常生产环境中不建议长时间开启，可能导致通信总线阻塞和系统性能下降。,
WPX→PC,0x0009,ASK调制协议数据包,变长数据包结构，符合WPC无线充电联盟ASK调制规范,"变长数据帧
最大32 Bytes",NO,无单位,ASK(振幅键控)调制协议数据包实时上报模块。前置条件：需先执行0x0008命令启用协议数据流监控。数据包内容严格遵循WPC标准规范，用于实时协议监控、信号分析和通信质量评估。,
WPX→PC,0x000A,FSK调制协议数据包,变长数据包结构，符合WPC无线充电联盟FSK调制规范,"变长数据帧
最大32 Bytes",NO,无单位,FSK(频移键控)调制协议数据包实时上报模块。前置条件：需先执行0x0008命令启用协议数据流监控。数据包内容严格遵循WPC标准规范，提供高级协议通信的详细数据分析和性能监控能力。,
PC→WPX,0x000B,系统软件复位,无命令参数,命令：0 Bytes,NO,无单位,系统软件复位模块。执行软件级系统重启，重新初始化所有软件模块、清理内存状态、重置通信接口。不影响硬件配置和存储数据。适用于系统故障恢复、状态清理和运行环境重置。相比硬件复位更加温和安全。,
PC→WPX,0x000C,测试负载类型配置,"负载模式选择参数(1 Byte)：
• 0x00=系统内置电子负载
• 0x01=外部连接物理负载",命令：1 Byte,YES,无单位,测试负载类型配置模块。支持内置电子负载和外部物理负载两种测试模式。内置负载提供精确的电子控制，外部负载支持真实设备测试。配置将影响功率控制策略、保护机制和测量精度。适配不同测试场景和验证需求。,
PC→WPX,0x000D,协议功率参数配置,"协议选择(1 Byte)：
• 0x00=BPP基础功率协议配置
• 0x01=EPP扩展功率协议配置  
• 0x02=MPP磁吸功率协议配置

控制模式(1 Byte)：
• 0x00=启用系统默认参数配置
• 0x01=启用用户手动参数配置

电压设置(2 Bytes)：目标输出电压值
电流设置(2 Bytes)：目标输出电流值

注意事项：
• 电压精度：1000mV增量步进
• 电流精度：10mA增量步进",命令：6 Bytes,YES,"电压：1000mV步进
电流：10mA步进",无线充电协议功率参数配置模块。支持BPP/EPP/MPP三大协议的独立功率参数设置。提供系统默认和用户自定义两种配置模式。电压电流参数将永久存储，设备重启后自动加载。精确的步进控制确保功率输出的稳定性和可重复性。,
PC→WPX,0x000E,实时功率在线调节,"控制模式(1 Byte)：
• 0x00=恢复系统默认功率控制
• 0x01=启用手动实时功率控制

电压设置(2 Bytes)：实时调节目标电压
电流设置(2 Bytes)：实时调节目标电流

特性：实时生效，不保存至非易失性存储器",命令：5 Bytes,NO,"电压：1000mV步进
电流：10mA步进",实时功率调节模块。支持任意协议状态下的电压电流在线调节，立即生效无需重启。调节参数为临时设置，设备重启后恢复至存储的配置参数。适用于功率特性测试、动态负载模拟和实时性能验证。,
PC→WPX,0x000F,协议补偿参数设置,"协议选择(1 Byte)：
• 0x00=BPP协议补偿参数
• 0x01=EPP协议补偿参数
• 0x02=MPP协议补偿参数

控制模式(1 Byte)：
• 0x00=自动默认补偿算法
• 0x01=手动补偿参数设置

电压补偿(2 Bytes,有符号16位整数)：
• 补偿范围：-1000mV ~ +1000mV
• 分辨率：10mV

电流补偿(2 Bytes,有符号16位整数)：
• 补偿范围：-1000mA ~ +1000mA  
• 分辨率：10mA

频率补偿(2 Bytes,有符号16位整数)：
• 补偿范围：-10kHz ~ +10kHz
• 分辨率：1kHz",命令：8 Bytes,YES,"电压：10mV分辨率
电流：10mA分辨率  
频率：1kHz分辨率",协议功率补偿参数配置模块。针对不同无线充电协议提供精细化的补偿调节能力。支持电压、电流、频率三维补偿，有效改善功率传输效率和兼容性。补偿算法考虑温度漂移、器件差异和环境因素，确保稳定的充电性能。,
PC→WPX,0x0010,实时补偿在线调节,"电压补偿(2 Bytes,有符号16位整数)
电流补偿(2 Bytes,有符号16位整数)  
频率补偿(2 Bytes,有符号16位整数)

特性：立即生效，临时调节，不保存配置",命令：6 Bytes,NO,"电压：10mV分辨率
电流：10mA分辨率
频率：1kHz分辨率",实时补偿参数在线调节模块。支持补偿参数的动态调节而无需设备重启。调节立即生效但不写入非易失性存储器，适用于补偿算法验证、动态校准和实时性能优化测试。,
PC→WPX,0x0011,FOD异物检测补偿设置,"FOD RPP功率补偿(2 Bytes,有符号16位整数)：
• 补偿范围：-2000 ~ +2000
• 分辨率：10mW

FOD Vrect整流电压补偿(2 Bytes,有符号16位整数)：
• 补偿范围：-2000 ~ +2000  
• 分辨率：10mW

FOD IRect整流电流补偿(2 Bytes,有符号16位整数)：
• 补偿范围：-2000 ~ +2000
• 分辨率：10mW

PRect整流功率补偿(2 Bytes,有符号16位整数)：
• 补偿范围：-2000 ~ +2000
• 分辨率：10mW",命令：8 Bytes,YES,功率：10mW分辨率,FOD(Foreign Object Detection)异物检测补偿参数配置模块。通过精确调节功率、电压、电流补偿参数，优化异物检测算法的灵敏度和准确性。防止金属异物引起的过热、效率下降和安全隐患，确保无线充电系统的安全可靠运行。,
PC→WPX,0x0012,系统测试模式配置,"测试模式选择(1 Byte)：
• 0x00=普通测试模式(标准速度,完整测试流程)
• 0x01=快速测试模式(加速测试,缩短等待时间)  
• 0x02=兼容测试模式(最大兼容性,延长超时时间)",命令：1 Byte,YES,无单位,系统测试模式配置模块。提供三种测试策略以适应不同的测试需求和时间要求。普通模式确保测试完整性，快速模式提高测试效率，兼容模式最大化设备兼容性。配置参数影响测试超时、重试机制和判定阈值。,
PC→WPX,0x0013,Qi2.2协议模式设置,"Qi2.2模式选择(1 Byte)：
• 0x00=强制模式(严格按Qi2.2规范执行)
• 0x01=标准模式(兼容旧版本协议)",命令：1 Byte,YES,无单位,Qi2.2新一代无线充电协议模式配置。强制模式严格遵循Qi2.2规范要求，确保最新功能的完整支持；标准模式保持向后兼容性，支持与旧版本设备的互操作。影响功率协商、通信协议和安全机制的执行策略。,
PC→WPX,0x0014,ASK调制参数配置,"通道配置(1 Byte)：
• 0x00=单通道ASK调制  
• 0x01=双通道ASK调制

载波深度配置(1 Byte)：
• 0x00=标准载波调制深度
• 0x01=高载波调制深度",命令：2 Bytes,YES,无单位,ASK(振幅键控)调制参数配置模块。支持单通道和双通道调制模式，以及标准和高载波调制深度设置。配置影响信号传输质量、通信距离和干扰抗性。双通道模式提供更高的数据传输速率，高调制深度增强信号稳定性。,
PC→WPX,0x0015,EPP协议版本选择,"EPP协议版本(1 Byte)：
• 0x00=Qi 1.2版本EPP协议
• 0x01=Qi 1.3版本EPP协议  
• 0x02=Qi 2.0版本EPP协议",命令：1 Byte,YES,无单位,EPP(Extended Power Profile)扩展功率协议版本选择模块。支持Qi 1.2、1.3、2.0三个主要版本的EPP协议。不同版本在功率等级、通信机制、安全特性方面存在差异。版本选择影响最大功率输出、鉴权机制和兼容性范围。,
PC→WPX,0x0016,MPP协议版本选择,"MPP协议版本(1 Byte)：
• 0x00=Qi 2.0版本MPP协议
• 0x01=Qi 2.1版本MPP协议
• 0x02=Qi 2.2版本MPP协议",命令：1 Byte,YES,无单位,MPP(Magnetic Power Profile)磁吸功率协议版本选择模块。支持Qi 2.0、2.1、2.2版本的MPP协议，主要用于磁吸式无线充电设备。各版本在磁场定位、功率传输效率、通信协议方面持续优化，版本选择需匹配目标设备规范。,
PC→WPX,0x0017,厂商私有协议类型选择,"厂商私有协议类型(1 Byte)：
• 0x03=MPP_25W_MODE(25W磁吸快充模式)
• 0x04=APPLE_MODE(苹果标准模式)  
• 0x05=APPLE_MAGSAFE_MODE(苹果MagSafe模式)
• 0x06=APPLE_FAKE_MAGSAFE_25W_MODE(仿MagSafe 25W模式)
• 0x07=SS_PPDE_MODE(三星PPDE协议模式)
• 0x08=SS_FC_MODE(三星快充模式)
• 0x09=MI_MODE(小米私有协议模式)  
• 0x0A=HUAWEI_MODE(华为私有协议模式)",命令：1 Byte,YES,无单位,厂商私有协议类型配置模块。支持主流厂商的专有无线充电协议，包括苹果MagSafe系列、三星快充协议、小米私有协议、华为私有协议等。各协议在功率等级、通信机制、安全认证方面具有独特特性，确保与特定品牌设备的最佳兼容性。,
PC→WPX,0x0018,协议在线动态切换,"协议模式代码(1 Byte)：
标准协议：
• 0x00=BPP_MODE(基础功率协议)
• 0x01=EPP_MODE(扩展功率协议)  
• 0x02=MPP_MODE(磁吸功率协议)

扩展协议：  
• 0x03=MPP_25W_MODE(25W磁吸模式)
• 0x04=APPLE_MODE(苹果7.5W模式)
• 0x05=APPLE_MAGSAFE_MODE(MagSafe 15W模式)
• 0x06=APPLE_FAKE_MAGSAFE_25W_MODE(仿MagSafe 25W)
• 0x07=SS_PPDE_MODE(三星PPDE模式)
• 0x08=SS_FC_MODE(三星快充模式)  
• 0x09=MI_MODE(小米私有模式)
• 0x0A=HUAWEI_MODE(华为私有模式)
• 0x0B=OPPO_MODE(OPPO私有模式)
• 0x0C=VIVO_MODE(VIVO私有模式)
• 0x0D=REDMI_MODE(红米私有模式)
• 0x0E=REALME_MODE(真我私有模式)  
• 0x0F=IQOO_MODE(iQOO私有模式)
• 0x10=MEIZU_MODE(魅族私有模式)
• 0x11=TRANSSION_MODE(传音私有模式)
• 0x12=HONOR_MODE(荣耀私有模式)
• 0x13=PROPRIETARY_MODE(通用私有模式)
• 0x14=OTHER_MODE(其他扩展模式)",命令：1 Byte,NO,无单位,无线充电协议在线动态切换模块。支持在不重启设备的情况下实时切换协议类型，包括WPC标准协议和主流厂商私有协议。临时切换不保存配置，适用于协议兼容性测试、设备适配验证和动态协议协商场景。,
PC→WPX,0x0019,自定义数据包发送配置,"数据包内容(0-32 Bytes，变长)：
用户自定义的原始数据包内容，数据格式需符合WPC无线充电联盟技术规范","命令：0-32 Bytes
变长数据帧",实现状态待确认,无单位,自定义协议数据包发送模块。允许测试设备(RX)向充电器(TX)发送用户定义的原始数据包。数据包内容需严格遵循WPC规范格式要求，用于协议扩展测试、非标准协议验证和通信机制研究。,
PC→WPX,0x001A,TX响应数据包查询,"查询TX设备返回的响应数据包(0-32 Bytes，变长)：
前置条件：需先通过0x0019命令发送自定义数据包","响应：0-32 Bytes
变长数据帧",实现状态待确认,无单位,TX设备响应数据包查询模块。获取充电器(TX)对自定义数据包的响应内容。必须先执行0x0019命令发送数据包后才能查询响应。用于双向通信测试、协议握手验证和TX设备能力探测。,
PC→WPX,0x001B,设备电量状态包发送,"电量百分比(1 Byte)：
• 数值范围：0-100  
• 单位：百分比(%)
• 用途：模拟RX设备向TX发送当前电池电量信息",命令：1 Byte,实现状态待确认,百分比(%),设备电量状态包发送模块。模拟接收设备(RX)向充电器(TX)发送电池电量信息的标准协议行为。电量信息影响TX的充电策略、功率调节和充电终止判断。用于充电逻辑测试和电量管理验证。,
PC→WPX,0x001C,EPT停充包发送,"EPT(End Power Transfer)停充原因代码(1 Byte)：
• 0x01=EPT_CHARGE_FULL(充电完成)
• 0x02=EPT_INTERNAL_FAIL(内部故障)  
• 0x03=EPT_OTP(过温保护)
• 0x04=EPT_OVP(过压保护)
• 0x05=EPT_OCP(过流保护)
• 0x06=EPT_BATTERY_FAIL(电池故障)
• 0x08=EPT_NO_RESPONSE(无响应超时)
• 0x0A=EPT_ABORTED_NEGOTIATION(协商中止)  
• 0x0B=EPT_RESTART(重启请求)
• 0x0C=EPT_REPING(重新探测)
• 0x0D=EPT_NFC(NFC干扰)
• 0x0E=EPT_RESERVED_0x0E(保留代码0x0E)
• 0x0F=EPT_RESERVED_0x0F(保留代码0x0F)",命令：1 Byte,实现状态待确认,无单位,EPT(End Power Transfer)停充包发送模块。模拟RX设备向TX发送停止充电请求，包含详细的停充原因代码。TX收到EPT包后将立即停止功率传输。用于充电终止逻辑测试、安全保护验证和异常情况模拟。,
PC→WPX,0x001D,设备鉴权流程启动,"鉴权协议类型(1 Byte)：
• 0x00=启动EPP 1.3版本安全鉴权流程
• 0x01=启动MPP协议安全鉴权流程",命令：1 Byte,实现状态待确认,无单位,无线充电设备安全鉴权流程启动模块。根据协议类型启动相应的设备身份认证和安全验证过程。鉴权成功后设备可获得更高功率等级和高级功能权限。用于安全机制测试和设备认证验证。,
WPX→PC,0x001E,设备鉴权数据上报,"鉴权证书数据包结构(变长，鉴权完成后连续上报3次)：

子数据包格式规范：
• 包头标识：0xEF(1 Byte)  
• 数据长度：子数据包长度(2 Bytes)
• 数据内容：对应的证书字段数据

鉴权证书字段清单：
1. Brand(品牌信息)
2. Product Model(产品型号)  
3. validity.notBefore(证书生效时间)
4. validity.notAfter(证书失效时间)
5. serialNumber(证书序列号)
6. subjectPublicKey(设备公钥)
7. wpc-qi-rsid(WPC Qi资源标识符)  
8. signature(数字签名)","变长数据帧
多个子包组合
上报频次：3次",实现状态待确认,无单位,设备鉴权证书数据上报模块。当设备鉴权流程完成后，自动上报完整的数字证书信息。证书包含设备身份、制造商信息、有效期、公钥、数字签名等关键安全要素。数据以子包格式组织，确保完整性和可解析性。用于安全认证验证和证书链分析。,
PC→WPX,0x001F,协议自动检测控制,"自动检测控制(1 Byte)：  
• 0x00=关闭协议自动检测功能
• 0x01=开启协议自动检测功能

注意事项：测试过程中会出现断充现象，这是由于协议切换导致的正常行为",命令：1 Byte,实现状态待确认,无单位,无线充电协议自动检测控制模块。开启后系统将自动遍历测试所有支持的协议类型，检测设备兼容性和性能表现。检测过程中由于协议切换会出现断充现象属于正常行为。用于设备兼容性全面评估和协议支持能力验证。,
WPX→PC,0x0020,协议检测结果数据上报,"协议自动检测结果数据结构(17 Bytes)：

测试状态(1 Byte,偏移0)：
• 0x00=检测未开始  
• 0x01=检测进行中
• 0x02=检测完成

当前测试协议(1 Byte,偏移1)：
• 0x00=NONE(无协议)
• 0x01=BPP_MODE  
• 0x02=EPP_MODE
• 0x03=MPP_MODE
• 0x04=MPP_25W_MODE
• 0x05=APPLE_MODE  
• 0x06=APPLE_MAGSAFE_15W_MODE
• 0x07=APPLE_FAKE_MAGSAFE_25W_MODE
• 0x08=SS_PPDE_MODE
• 0x09=SS_FC_MODE
• 0x0A=MI_MODE
• 0x0B=HUAWEI_MODE

协议测试结果详情：
• BPP测试结果(1 Byte,偏移2)
• EPP测试结果(1 Byte,偏移3)  
• EPP握手功率(2 Bytes,偏移4-5,单位mW)
• MPP握手结果(1 Byte,偏移6)
• MPP握手功率(2 Bytes,偏移7-8,单位mW)  
• MPP25W测试结果(1 Byte,偏移9)
• APPLE 7.5W测试结果(1 Byte,偏移10)
• MagSafe 15W测试结果(1 Byte,偏移11)
• MagSafe 25W测试结果(1 Byte,偏移12)
• PPDE测试结果(1 Byte,偏移13)
• FC测试结果(1 Byte,偏移14)  
• MI测试结果(1 Byte,偏移15)
• HUAWEI测试结果(1 Byte,偏移16)

测试结果状态码：
• 0x00=初始状态(未测试)
• 0x02=测试成功  
• 0x03=测试失败
• 0x04暂不支持测试","固定17 Bytes
上报策略：
• 单个协议完成：立即上报
• 全部完成：连续上报3次",实现状态待确认,"功率：mW
状态：枚举值",协议自动检测结果数据上报模块。提供详细的多协议兼容性测试结果，包括测试状态、当前协议、各协议测试结果和握手功率等关键信息。单个协议测试完成后立即上报当前状态，全部协议检测完成后连续上报3次最终结果，确保数据完整性和可靠性。,
PC→WPX,0x0021,系统掉电计数器清零,无命令参数,命令：0 Bytes,实现状态待确认,无单位,系统掉电次数计数器清零模块。清除监控模式下记录的意外掉电次数统计。掉电计数用于评估电源稳定性、系统可靠性和运行环境质量。清零操作通常在维护、校准或重新部署时执行，重置统计基线。,
