<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>DialogSettings</class>
 <widget class="QDialog" name="DialogSettings">
  <property name="windowModality">
   <enum>Qt::ApplicationModal</enum>
  </property>
  <property name="enabled">
   <bool>true</bool>
  </property>
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>303</width>
    <height>460</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>303</width>
    <height>460</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>303</width>
    <height>460</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>系统设置</string>
  </property>
  <property name="sizeGripEnabled">
   <bool>false</bool>
  </property>
  <property name="modal">
   <bool>true</bool>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <widget class="QLabel" name="label_2">
       <property name="text">
        <string>上位机版本：</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignCenter</set>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLabel" name="label_3">
       <property name="text">
        <string>v1.0.0</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignCenter</set>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_33" stretch="0">
     <item>
      <widget class="QPushButton" name="pushButton">
       <property name="text">
        <string>检查更新</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="Line" name="line">
     <property name="frameShadow">
      <enum>QFrame::Plain</enum>
     </property>
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
    </widget>
   </item>
   <item>
    <layout class="QVBoxLayout" name="verticalLayout_2">
     <item>
      <widget class="QLabel" name="label_44">
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>0</height>
        </size>
       </property>
       <property name="font">
        <font>
         <family>Microsoft YaHei</family>
        </font>
       </property>
       <property name="text">
        <string>- WPX固件更新 -</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignCenter</set>
       </property>
       <property name="margin">
        <number>6</number>
       </property>
      </widget>
     </item>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_2">
       <item>
        <widget class="QLabel" name="label">
         <property name="text">
          <string>固件版本：</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="fw_version_lb">
         <property name="text">
          <string>0.0.0</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
        </widget>
       </item>
      </layout>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QPushButton" name="btnFWupdatekKey">
     <property name="text">
      <string>固件更新</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="Line" name="line_2">
     <property name="frameShadow">
      <enum>QFrame::Plain</enum>
     </property>
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_3" stretch="1">
     <property name="spacing">
      <number>6</number>
     </property>
     <property name="bottomMargin">
      <number>0</number>
     </property>
     <item>
      <widget class="QPushButton" name="btnOk">
       <property name="font">
        <font>
         <family>Microsoft YaHei</family>
        </font>
       </property>
       <property name="text">
        <string>OK</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
