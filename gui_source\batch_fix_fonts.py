#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量修复字体混叠问题
使用sed命令批量替换PingFang为Microsoft YaHei
"""

import subprocess
import os
import sys

def run_command(cmd, cwd=None):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(cmd, shell=True, cwd=cwd, capture_output=True, text=True, encoding='utf-8')
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def main():
    """主函数"""
    print("开始批量修复字体混叠问题...")
    
    # 获取当前目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    template_dir = os.path.join(current_dir, "mdp_gui_template")
    
    # 需要修复的文件列表
    files_to_fix = [
        "mainwindow.ui",
        "settings.ui", 
        "updates.ui",
        "graphics.ui",
        "mainwindow_ui.py",
        "settings_ui.py",
        "updates_ui.py", 
        "graphics_ui.py",
        "applestyledisplay.py"
    ]
    
    for filename in files_to_fix:
        filepath = os.path.join(template_dir, filename)
        if os.path.exists(filepath):
            print(f"正在修复: {filename}")
            
            # 读取文件
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 统计原始PingFang数量
                original_count = content.count('PingFang')
                
                # 替换PingFang为Microsoft YaHei
                new_content = content.replace('PingFang', 'Microsoft YaHei')
                
                # 统计替换后的数量
                new_count = new_content.count('Microsoft YaHei')
                
                # 写回文件
                with open(filepath, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                
                print(f"  ✓ 替换了 {original_count} 处PingFang字体")
                
            except Exception as e:
                print(f"  ✗ 修复失败: {e}")
        else:
            print(f"文件不存在，跳过: {filename}")
    
    print("\n字体混叠修复完成！")
    print("如果修复的是.ui文件，请重新编译:")
    print("  python compile_ui.py")

if __name__ == "__main__":
    main()
