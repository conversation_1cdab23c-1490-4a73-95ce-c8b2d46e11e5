@echo off
REM UI Compilation Batch Script for Windows
REM Compiles .ui files to .py files using PyQt5 tools

echo === UI Compilation Script ===
echo Compiling .ui files to .py files for WPX-QT-GUI
echo.

cd /d "%~dp0"

REM Check if pyuic5 is available
pyuic5 --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: pyuic5 not found. Please install PyQt5-tools:
    echo   pip install PyQt5-tools
    exit /b 1
)

echo Found pyuic5, proceeding with compilation...
echo.

REM Compile UI files
echo Compiling mainwindow.ui...
pyuic5 mdp_gui_template\mainwindow.ui -o mdp_gui_template\mainwindow_ui.py
if %errorlevel% neq 0 goto :error

echo Compiling settings.ui...
pyuic5 mdp_gui_template\settings.ui -o mdp_gui_template\settings_ui.py
if %errorlevel% neq 0 goto :error

echo Compiling updates.ui...
pyuic5 mdp_gui_template\updates.ui -o mdp_gui_template\updates_ui.py
if %errorlevel% neq 0 goto :error

echo Compiling graphics.ui...
pyuic5 mdp_gui_template\graphics.ui -o mdp_gui_template\graphics_ui.py
if %errorlevel% neq 0 goto :error

echo.
echo === Compilation Complete ===
echo All UI files compiled successfully!
goto :end

:error
echo.
echo === Compilation Failed ===
echo One or more UI files failed to compile
exit /b 1

:end
pause