"""
系统资源保护对话框
在开始数据记录前检查系统资源是否充足，防止程序崩溃
"""

from PyQt5 import QtWidgets, QtCore, QtGui
from PyQt5.QtCore import Qt
from system_resource_protection import SystemResourceProtection
from loguru import logger


class ResourceProtectionDialog(QtWidgets.QDialog):
    """系统资源保护检查对话框"""
    
    def __init__(self, parent=None, sampling_rate=1000, duration_hours=12.0):
        super().__init__(parent)
        self.sampling_rate = sampling_rate
        self.duration_hours = duration_hours
        self.protection = SystemResourceProtection()
        
        self.setWindowTitle("系统资源检查")
        self.setWindowFlags(Qt.Dialog | Qt.WindowCloseButtonHint)
        self.setModal(True)
        self.resize(600, 500)
        
        self.setup_ui()
        self.check_resources()
    
    def setup_ui(self):
        """设置UI界面"""
        layout = QtWidgets.QVBoxLayout(self)
        
        # 标题
        title_label = QtWidgets.QLabel("数据记录资源检查")
        title_label.setStyleSheet("font-family: PingFang; font-size: 16px; font-weight: bold; color: #2c3e50;")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # 当前设置信息
        settings_group = QtWidgets.QGroupBox("当前记录设置")
        settings_layout = QtWidgets.QFormLayout(settings_group)
        
        self.rate_label = QtWidgets.QLabel(f"{self.sampling_rate} SPS")
        self.duration_label = QtWidgets.QLabel(f"{self.duration_hours:.1f} 小时")
        
        settings_layout.addRow("采样率:", self.rate_label)
        settings_layout.addRow("记录时长:", self.duration_label)
        
        layout.addWidget(settings_group)
        
        # 系统信息
        system_group = QtWidgets.QGroupBox("系统信息")
        system_layout = QtWidgets.QVBoxLayout(system_group)
        
        self.system_info_text = QtWidgets.QTextEdit()
        self.system_info_text.setReadOnly(True)
        self.system_info_text.setMaximumHeight(120)
        system_layout.addWidget(self.system_info_text)
        
        layout.addWidget(system_group)
        
        # 资源检查结果
        check_group = QtWidgets.QGroupBox("资源检查结果")
        check_layout = QtWidgets.QVBoxLayout(check_group)
        
        self.result_text = QtWidgets.QTextEdit()
        self.result_text.setReadOnly(True)
        self.result_text.setMaximumHeight(150)
        check_layout.addWidget(self.result_text)
        
        layout.addWidget(check_group)
        
        # 建议设置
        suggestion_group = QtWidgets.QGroupBox("建议设置")
        suggestion_layout = QtWidgets.QVBoxLayout(suggestion_group)
        
        self.suggestion_text = QtWidgets.QTextEdit()
        self.suggestion_text.setReadOnly(True)
        self.suggestion_text.setMaximumHeight(100)
        suggestion_layout.addWidget(self.suggestion_text)
        
        layout.addWidget(suggestion_group)
        
        # 按钮
        button_layout = QtWidgets.QHBoxLayout()
        
        self.refresh_btn = QtWidgets.QPushButton("刷新检查")
        self.refresh_btn.clicked.connect(self.check_resources)
        
        self.disk_test_btn = QtWidgets.QPushButton("磁盘性能测试")
        self.disk_test_btn.clicked.connect(self.test_disk_performance)
        
        self.continue_btn = QtWidgets.QPushButton("继续记录")
        self.continue_btn.clicked.connect(self.accept)
        
        self.cancel_btn = QtWidgets.QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        
        button_layout.addWidget(self.refresh_btn)
        button_layout.addWidget(self.disk_test_btn)
        button_layout.addStretch()
        button_layout.addWidget(self.continue_btn)
        button_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(button_layout)
    
    def check_resources(self):
        """检查系统资源"""
        try:
            # 获取系统信息
            system_info = self.protection.get_system_info()
            self.display_system_info(system_info)
            
            # 检查内存要求
            is_sufficient, message, details = self.protection.check_memory_requirements(
                self.sampling_rate, self.duration_hours
            )
            
            # 显示检查结果
            self.display_check_result(is_sufficient, message, details)
            
            # 显示建议设置
            suggestions = self.protection.suggest_optimal_settings(self.duration_hours)
            self.display_suggestions(suggestions)
            
            # 根据检查结果设置按钮状态
            if is_sufficient:
                self.continue_btn.setStyleSheet("background-color: #27ae60; color: white;")
                self.continue_btn.setText("继续记录")
            else:
                self.continue_btn.setStyleSheet("background-color: #e74c3c; color: white;")
                self.continue_btn.setText("强制继续(风险)")
                
        except Exception as e:
            logger.error(f"资源检查失败: {e}")
            self.result_text.setPlainText(f"资源检查失败: {e}")
    
    def display_system_info(self, system_info):
        """显示系统信息"""
        if not system_info:
            self.system_info_text.setPlainText("无法获取系统信息")
            return
        
        memory = system_info.get('memory', {})
        disk = system_info.get('disk', {})
        cpu = system_info.get('cpu', {})
        
        info_text = f"""物理内存: {memory.get('total_gb', 0):.1f}GB (可用: {memory.get('available_gb', 0):.1f}GB, 使用率: {memory.get('percent', 0):.1f}%)
磁盘空间: {disk.get('total_gb', 0):.1f}GB (可用: {disk.get('free_gb', 0):.1f}GB, 使用率: {disk.get('percent', 0):.1f}%)
CPU: {cpu.get('count', 0)}核心 @ {cpu.get('freq_mhz', 0):.0f}MHz
系统: {system_info.get('platform', 'Unknown')}"""
        
        self.system_info_text.setPlainText(info_text)
    
    def display_check_result(self, is_sufficient, message, details):
        """显示检查结果"""
        if is_sufficient:
            self.result_text.setStyleSheet("color: #27ae60;")
            result_icon = "✓ "
        else:
            self.result_text.setStyleSheet("color: #e74c3c;")
            result_icon = "✗ "
        
        result_text = result_icon + message
        
        if details:
            result_text += f"\n\n详细信息:"
            result_text += f"\n• 总采样点数: {details.get('total_samples', 0):,}"
            result_text += f"\n• 数据存储需要: {details.get('required_virtual_memory_gb', 0):.2f}GB"
            result_text += f"\n• 绘图内存需要: {details.get('required_physical_memory_gb', 0):.2f}GB"
        
        self.result_text.setPlainText(result_text)
    
    def display_suggestions(self, suggestions):
        """显示建议设置"""
        if not suggestions:
            self.suggestion_text.setPlainText("无法生成建议设置")
            return
        
        suggestion_text = f"""基于您的系统配置，建议设置:
• 推荐采样率: {suggestions.get('recommended_sample_rate', 1)} SPS
• 最大采样率: {suggestions.get('max_sample_rate', 1)} SPS  
• 实际记录时长: {suggestions.get('actual_duration_hours', 0):.1f} 小时
• 最大采样点数: {suggestions.get('max_samples', 0):,}
• 可用内存: {suggestions.get('available_memory_gb', 0):.1f}GB"""
        
        self.suggestion_text.setPlainText(suggestion_text)
    
    def test_disk_performance(self):
        """测试磁盘性能"""
        try:
            # 显示测试进度
            self.disk_test_btn.setText("测试中...")
            self.disk_test_btn.setEnabled(False)
            QtWidgets.QApplication.processEvents()
            
            # 执行磁盘测试
            is_good, message = self.protection.check_disk_performance()
            
            # 显示测试结果
            QtWidgets.QMessageBox.information(
                self, 
                "磁盘性能测试结果", 
                message
            )
            
        except Exception as e:
            logger.error(f"磁盘性能测试失败: {e}")
            QtWidgets.QMessageBox.warning(
                self, 
                "测试失败", 
                f"磁盘性能测试失败: {e}"
            )
        finally:
            self.disk_test_btn.setText("磁盘性能测试")
            self.disk_test_btn.setEnabled(True)
    
    def update_settings(self, sampling_rate, duration_hours):
        """更新记录设置并重新检查"""
        self.sampling_rate = sampling_rate
        self.duration_hours = duration_hours
        
        self.rate_label.setText(f"{sampling_rate} SPS")
        self.duration_label.setText(f"{duration_hours:.1f} 小时")
        
        self.check_resources()


class ResourceMonitorWidget(QtWidgets.QWidget):
    """资源监控小部件，可嵌入主窗口"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.protection = SystemResourceProtection()
        self.timer = QtCore.QTimer()
        self.timer.timeout.connect(self.update_status)
        
        self.setup_ui()
        self.start_monitoring()
    
    def setup_ui(self):
        """设置UI"""
        layout = QtWidgets.QHBoxLayout(self)
        layout.setContentsMargins(5, 2, 5, 2)
        
        # 内存状态
        self.memory_label = QtWidgets.QLabel("内存: --")
        self.memory_label.setStyleSheet("font-family: PingFang; font-size: 11px; color: #7f8c8d;")
        
        # CPU状态  
        self.cpu_label = QtWidgets.QLabel("CPU: --")
        self.cpu_label.setStyleSheet("font-family: PingFang; font-size: 11px; color: #7f8c8d;")
        
        # 进程内存
        self.process_label = QtWidgets.QLabel("进程: --")
        self.process_label.setStyleSheet("font-family: PingFang; font-size: 11px; color: #7f8c8d;")
        
        layout.addWidget(QtWidgets.QLabel("系统状态:"))
        layout.addWidget(self.memory_label)
        layout.addWidget(self.cpu_label)
        layout.addWidget(self.process_label)
        layout.addStretch()
    
    def start_monitoring(self):
        """开始监控"""
        self.timer.start(2000)  # 每2秒更新一次
        self.update_status()
    
    def stop_monitoring(self):
        """停止监控"""
        self.timer.stop()
    
    def update_status(self):
        """更新状态显示"""
        try:
            resources = self.protection.monitor_runtime_resources()
            
            if resources:
                # 更新内存状态
                memory_percent = resources.get('system_memory_percent', 0)
                memory_color = self.get_status_color(memory_percent)
                self.memory_label.setText(f"内存: {memory_percent:.1f}%")
                self.memory_label.setStyleSheet(f"font-size: 11px; color: {memory_color};")
                
                # 更新CPU状态
                cpu_percent = resources.get('cpu_percent', 0)
                cpu_color = self.get_status_color(cpu_percent)
                self.cpu_label.setText(f"CPU: {cpu_percent:.1f}%")
                self.cpu_label.setStyleSheet(f"font-size: 11px; color: {cpu_color};")
                
                # 更新进程内存
                process_mb = resources.get('process_memory_mb', 0)
                process_color = "#3498db" if process_mb < 1000 else "#f39c12"
                self.process_label.setText(f"进程: {process_mb:.0f}MB")
                self.process_label.setStyleSheet(f"font-size: 11px; color: {process_color};")
                
        except Exception as e:
            logger.error(f"更新资源状态失败: {e}")
    
    def get_status_color(self, percent):
        """根据使用率获取状态颜色"""
        if percent < 60:
            return "#27ae60"  # 绿色
        elif percent < 80:
            return "#f39c12"  # 橙色
        else:
            return "#e74c3c"  # 红色
