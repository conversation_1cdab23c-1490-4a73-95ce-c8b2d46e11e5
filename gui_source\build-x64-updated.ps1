# WPX-GUI Build Script with UI Compilation
# Updated to handle font changes and UI compilation

Write-Host "> Starting WPX-GUI build process" -ForegroundColor Green

# Step 1: Compile UI files to ensure latest changes are included
Write-Host "> Step 1: Compiling UI files..." -ForegroundColor Yellow
try {
    if (Test-Path "compile_ui.bat") {
        & .\compile_ui.bat
        if ($LASTEXITCODE -ne 0) {
            Write-Host "UI compilation failed!" -ForegroundColor Red
            exit 1
        }
        Write-Host "✓ UI compilation completed successfully" -ForegroundColor Green
    } else {
        Write-Host "Warning: compile_ui.bat not found, skipping UI compilation" -ForegroundColor Orange
    }
} catch {
    Write-Host "Error during UI compilation: $_" -ForegroundColor Red
    exit 1
}

# Step 2: Build both versions in parallel
Write-Host "> Step 2: Building WPX-01 and WPX-01-Numba in parallel..." -ForegroundColor Yellow

# Start both builds in parallel
$job1 = Start-Job -ScriptBlock {
    Set-Location $using:PWD
    Write-Host "Starting WPX-01 build..."
    .\.venv\Scripts\pyinstaller.exe --noconfirm --upx-dir C:\Toolchains\upx .\mdp.spec --log-level WARN
}

$job2 = Start-Job -ScriptBlock {
    Set-Location $using:PWD
    Write-Host "Starting WPX-01-Numba build..."
    .\.venv\Scripts\pyinstaller.exe --noconfirm --upx-dir C:\Toolchains\upx .\mdp_numba.spec --log-level WARN
}

# Wait for both jobs to complete
Write-Host "> Waiting for builds to complete..." -ForegroundColor Yellow
Wait-Job $job1, $job2

# Get output from jobs
Write-Host "> WPX-01 build output:" -ForegroundColor Cyan
Receive-Job $job1
Write-Host "> WPX-01-Numba build output:" -ForegroundColor Cyan
Receive-Job $job2

# Check if builds were successful
$job1State = (Get-Job $job1).State
$job2State = (Get-Job $job2).State

# Clean up
Remove-Job $job1, $job2

# Report results
if ($job1State -eq "Completed" -and $job2State -eq "Completed") {
    Write-Host "> ✓ Build completed successfully!" -ForegroundColor Green
    Write-Host "> Built executables:" -ForegroundColor Cyan
    if (Test-Path "dist\WPX-GUI.exe") {
        $size1 = [math]::Round((Get-Item "dist\WPX-GUI.exe").Length / 1MB, 2)
        Write-Host "  - WPX-GUI.exe ($size1 MB)" -ForegroundColor White
    }
    if (Test-Path "dist\WPX-01-Numba.exe") {
        $size2 = [math]::Round((Get-Item "dist\WPX-01-Numba.exe").Length / 1MB, 2)
        Write-Host "  - WPX-01-Numba.exe ($size2 MB)" -ForegroundColor White
    }
} else {
    Write-Host "> ✗ Build failed!" -ForegroundColor Red
    exit 1
}