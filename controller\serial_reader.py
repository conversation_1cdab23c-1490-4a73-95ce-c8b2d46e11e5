import queue
import threading
import time
import struct
from queue import Queue
from typing import Tuple, Union

from loguru import logger
from numpy import byte
from serial import Serial


class SerialReader:
    def __init__(self, serial_instance: Serial):
        """
        初始化串口读取器。

        Args:
            serial_instance (Serial): 串口实例。
            start_bit (list): 读取起始位。
            checksum (bool, optional): 是否启用校验。Defaults to True。

        Returns:
            None
        """
        self.ascii_queue = queue.Queue()
        self.binary_queue = queue.Queue()
        self._ser = serial_instance
        self._ascii_buffer = bytes()
        self._binary_buffer = bytes()
        while self._ser.in_waiting > 0:  # 清空缓冲区
            self._ser.read(self._ser.in_waiting)

    def read(self) -> int:
        """
        轮询读取串口数据，支持整包传输。

        Returns:
            int: 读取到的数据类型。
            - 0: 无数据
            - 1: 读取到ASCII数据
            - 2: 读取到二进制数据
        """
        ret = 0
        
        # 调试：记录调用次数和串口状态
        if not hasattr(self, '_serial_read_count'):
            self._serial_read_count = 0
        self._serial_read_count += 1

        # 检查串口是否有数据 - 增加异常处理
        try:
            if not self._ser.is_open:
                # 串口已关闭，抛出异常让上层检测到断开
                raise Exception("Serial port is closed")
                
            in_waiting = self._ser.in_waiting
            if self._serial_read_count % 10000 == 1:  # 每10000次记录一次
                logger.info(f"SerialReader.read() call #{self._serial_read_count}: in_waiting={in_waiting}")
        except Exception as e:
            # 串口异常（可能设备断开连接），向上抛出异常
            if self._serial_read_count % 1000 == 1:  # 每1000次记录一次错误
                logger.warning(f"Serial port error during read #{self._serial_read_count}: {e}")
            # 重要：向上抛出异常以便上层检测设备断开
            raise e
        
        try:
            while self._ser.in_waiting > 0:
                # 读取所有可用数据
                data = self._ser.read(self._ser.in_waiting)
                # logger.info(f"SerialReader: read {len(data)} bytes from serial port")
                data = data.hex()  # 将读取到的字节数据转换为16进制字符串

            # 检查帧头是否为0xEE，否则丢弃数据
            # if data[0] != 0xEE:  # SOF必须为0xEE
            #     continue

            # logger.info(f"Read data (hex): {data}")
            # self._binary_buffer += data

            # 循环处理缓冲区中的所有可能帧
            # while len(self._binary_buffer) >= 5:  # 至少需要5字节来检查帧头
            # logger.info(f"Binary buffer: {data}")
            # 检查帧头SOF
            # if data[0] != 0xEE:  # SOF必须为0xEE
            #     # 无效帧头，移除第一个字节，继续检查
            #     data = data[1:]
            #     continue

            # # 提取帧头（5字节）
            # frame_header = data[:5]
            # sof, data_length, seq, crc8 = struct.unpack('>BHBB', frame_header)

            # 验证帧头CRC8
            # expected_crc8 = self.calculate_crc8(frame_header[:4])  # CRC8基于前4字节
            # if crc8 != expected_crc8:
            #     logger.warning(f"Frame header CRC8 check failed: expected {expected_crc8}, got {crc8}")
            #     self._binary_buffer = self._binary_buffer[1:]
            #     continue

            # 计算整帧长度：5字节帧头 + 2字节cmd_id + data_length字节数据 + 2字节frame_tail（可选）
            # total_frame_length = 5 + 2 + data_length + 2  # 假设frame_tail存在
            # if len(data) < total_frame_length:
            #     # 数据不足以构成完整帧，等待更多数据
            #     break

            # # 提取整帧
            # frame = data[:total_frame_length]
            # data = data[total_frame_length:]

            # # 提取cmd_id和data
            # cmd_id = struct.unpack('>H', frame[5:7])[0]
            # data = frame[7:7 + data_length]

            # 验证frame_tail（CRC16，可选）
            # frame_tail = frame[-2:]
            # 这里可以添加CRC16校验逻辑，示例中暂时跳过
            # expected_crc16 = calculate_crc16(frame[:-2])
            # if frame_tail != expected_crc16:
            #     logger.warning("Frame CRC16 check failed")
            #     continue

                # 帧有效，放入队列
                self.binary_queue.put(data)
                # logger.info(f"Read binary frame: {frame}")
                ret |= 2
        except Exception as e:
            # 串口读取异常（可能设备断开连接）
            if self._serial_read_count % 1000 == 1:  # 每1000次记录一次错误
                logger.warning(f"Serial port read error #{self._serial_read_count}: {e}")
            # 重要：向上抛出异常以便上层检测设备断开
            raise e
        
        return ret

    def clear(self):
        """
        清空缓冲区。
        """
        self._ascii_buffer = bytes()

    def close(self):
        """
        关闭串口连接。

        Returns:
            None
        """
        self._ser.close()


class SerialReaderThreaded:
    """
    多线程的串口读取器
    """

    def __init__(self, serial_instance: Serial):
        """
        初始化串口读取器。
        """
        self._queue: Queue[Tuple[int, bytes]] = Queue()
        self._serial_reader = SerialReader(serial_instance)
        self._thread_running = True
        self._thread = threading.Thread(target=self._worker, daemon=True)
        self._thread.start()

    def _worker(self):
        while self._thread_running:
            if self._serial_reader.read():
                self._queue.put(self._serial_reader.ascii_result)
            else:
                time.sleep(0.001)

    def read(self) -> bool:
        """
        是否有数据可读
        """
        return not self._queue.empty()

    @property
    def result(self) -> bytes:
        """
        读取数据(阻塞,一个数据仅能读取一次)
        """
        return self._queue.get()

    def close(self, join=True):
        """
        关闭串口连接。

        Returns:
            None
        """
        self._thread_running = False
        if join:
            self._thread.join()
        self._serial_reader.close()


SerialReaderLike = Union[SerialReader, SerialReaderThreaded]
