import queue
import time
import struct
from typing import Optional, Self

from loguru import logger
from serial import Serial
from serial.tools import list_ports

from controller.serial_reader import SerialReader
from controller.wpx_protocol import WPXProtocol


def find_all_devices() -> list:
    """
    Find all WPX devices and return a list of (port_name, description) tuples.
    """
    VID = 0x0906
    PID = 0x5A3E
    
    matching_devices = []
    for port in list_ports.comports():
        if PID == port.pid and VID == port.vid:
            matching_devices.append((port.device, port.description))
    
    return matching_devices


def find_bootloader_devices() -> list:
    """
    Find all WPX bootloader devices and return a list of (port_name, description) tuples.
    Bootloader PID:VID = 87E7:5A42
    """
    VID = 0x07E7
    PID = 0x5A42
    
    
    matching_devices = []
    for port in list_ports.comports():
        if PID == port.pid and VID == port.vid:
            matching_devices.append((port.device, port.description))
    
    return matching_devices


def get_all_serial_devices_info() -> list:
    """
    Get information about all serial devices with their PID/VID.
    Returns a list of (port_name, description, vid, pid) tuples.
    """
    devices_info = []
    for port in list_ports.comports():
        vid = port.vid if port.vid else 0
        pid = port.pid if port.pid else 0
        devices_info.append((
            port.device, 
            port.description, 
            f"{vid:04X}" if vid else "N/A",
            f"{pid:04X}" if pid else "N/A"
        ))
    
    return devices_info


def wait_for_bootloader_device(timeout: float = 10.0, check_interval: float = 0.5) -> Optional[str]:
    """
    Wait for a bootloader device to appear.
    
    Args:
        timeout: Maximum time to wait in seconds
        check_interval: Time between checks in seconds
        
    Returns:
        Port name of the first bootloader device found, or None if timeout
    """
    import time
    start_time = time.time()
    
    while time.time() - start_time < timeout:
        devices = find_bootloader_devices()
        if devices:
            if len(devices) == 1:
                logger.info(f"Found bootloader device: {devices[0][0]} - {devices[0][1]}")
                return devices[0][0]
            else:
                # 多个bootloader设备，返回第一个并记录日志
                logger.info(f"Found {len(devices)} bootloader devices, using first one: {devices[0][0]}")
                for i, (port, desc) in enumerate(devices):
                    logger.info(f"  {i+1}. {port}: {desc}")
                return devices[0][0]
        time.sleep(check_interval)
    
    logger.warning(f"Timeout waiting for bootloader device after {timeout}s")
    return None


def wait_for_bootloader_device_with_callback(timeout: float = 10.0, check_interval: float = 0.5, 
                                            multi_device_callback = None) -> Optional[str]:
    """
    Wait for a bootloader device to appear, with callback for multiple device selection.
    
    Args:
        timeout: Maximum time to wait in seconds
        check_interval: Time between checks in seconds
        multi_device_callback: Callback function to handle multiple device selection
                             Should return the selected port name or None to cancel
        
    Returns:
        Port name of the selected bootloader device, or None if timeout/cancelled
    """
    import time
    start_time = time.time()
    
    while time.time() - start_time < timeout:
        devices = find_bootloader_devices()
        if devices:
            if len(devices) == 1:
                logger.info(f"Found bootloader device: {devices[0][0]} - {devices[0][1]}")
                return devices[0][0]
            else:
                # 多个bootloader设备，调用回调函数让用户选择
                logger.info(f"Found {len(devices)} bootloader devices")
                if multi_device_callback:
                    selected_port = multi_device_callback(devices)
                    if selected_port:
                        logger.info(f"User selected bootloader device: {selected_port}")
                        return selected_port
                    else:
                        logger.info("User cancelled bootloader device selection")
                        return None
                else:
                    # 没有回调函数，使用第一个设备
                    logger.info(f"No callback provided, using first bootloader device: {devices[0][0]}")
                    return devices[0][0]
        time.sleep(check_interval)
    
    logger.warning(f"Timeout waiting for bootloader device after {timeout}s")
    return None


def _find_port_name() -> str:
    """
    Find port name for WPX device. If multiple devices found, raise exception with device list.
    """
    # VID = 0x5A3E
    # PID = 0x0906

    VID = 0x0906
    PID = 0x5A3E
    
    # Find all matching devices
    matching_devices = []
    for port in list_ports.comports():
        if PID == port.pid and VID == port.vid:
            matching_devices.append(port)
    
    # If exactly one device found, return it
    if len(matching_devices) == 1:
        return matching_devices[0].device
    
    # If multiple devices found, raise exception with device list
    if len(matching_devices) > 1:
        device_list = ""
        for i, port in enumerate(matching_devices):
            device_list += f"{i+1}. {port.device}: {port.description}\n"
        raise Exception(
            f"发现多台WPX设备，请选择要连接的设备:\n{device_list}"
        )
    
    # No matching devices found
    listinfo = ""
    for port in list_ports.comports():
        if not port.pid or not port.vid:
            listinfo += f"{port.device}: {port.description}: No ID provided\n"
        else:
            listinfo += (
                f"{port.device}: {port.description} {port.vid:04X}:{port.pid:04X}\n"
            )
    if not listinfo:
        listinfo = "No device found"
    raise Exception(
        f"Cannot find WPX\n"
        f"请检查连接是否正确"
        #(Target PID:VID = {PID:04X}:{VID:04X})\nDevice List:\n{listinfo}"
    )


class PDPocket:
    def __init__(
        self,
        port: Optional[str] = None,
        baudrate: int = 921600,
        use_wpx_protocol: bool = False,
    ):
        """
        Initialize WPX  Power Supply Controller.
        """
        if not port:
            port = _find_port_name()
        self._ser = Serial(port=port, baudrate=baudrate)
        self._reader = SerialReader(self._ser)
        self._last_readout_v = 0
        self._last_readout_i = 0
        self._read_msg_queue = queue.Queue()
        self.async_mode = False
        
        # WPX Protocol support
        self.use_wpx_protocol = use_wpx_protocol
        if use_wpx_protocol:
            self.wpx_protocol = WPXProtocol()
            self.wpx_protocol.set_send_data_callback(self._send_wpx_data)
        else:
            self.wpx_protocol = None

    def close(self):
        self._ser.close()
        logger.info("WPX  closed")

    def read(self) -> int:
        try:
            result = self._reader.read()
        except Exception as e:
            logger.warning(f"Failed to read from serial: {e}")
            # 对于设备断开相关的异常，向上抛出以便上层检测
            if "ClearCommError" in str(e) or "PermissionError" in str(e) or "Serial port is closed" in str(e):
                raise e
            return 0
        
        # 调试：记录read调用和结果
        if not hasattr(self, '_read_call_count'):
            self._read_call_count = 0
        self._read_call_count += 1
        
        # if self._read_call_count % 5000 == 1:  # 每5000次记录一次
        #     logger.info(f"PDPocket.read() call #{self._read_call_count}: result={result}, in_waiting={self._ser.in_waiting}")
        
        # Process WPX protocol data if enabled
        if self.use_wpx_protocol and self.wpx_protocol and result == 2:
            try:
                # Get binary data from reader
                while not self._reader.binary_queue.empty():
                    data = self._reader.binary_queue.get(block=False)
                    if isinstance(data, str):
                        data = bytes.fromhex(data)
                    if data:
                        # logger.info(f"Processing WPX data: {len(data)} bytes")
                        self.wpx_protocol.process_received_data(data)
            except queue.Empty:
                pass
                
        return result

    def _read_until_ok(self, timeout: float = 0.05) -> bool:
        t0 = time.perf_counter()
        if not self.async_mode:
            while self._reader.ascii_queue.empty():
                self.read()
                if time.perf_counter() - t0 > timeout:
                    raise TimeoutError("Timeout waiting for response")
                time.sleep(0.01)
            return self._reader.ascii_queue.get().decode().strip()
        else:
            try:
                return self._reader.ascii_queue.get(timeout=timeout).decode().strip()
            except queue.Empty:
                raise TimeoutError("Timeout waiting for response")

    def get_output_voltage(self) -> float:
        """
        Get the output voltage of the WPX .
        """
        self._reader.clear()
        self._ser.write(b"MEAS:VOLT?")
        val = float(self._read_until_ok())
        # if val < 0.1:
        #     logger.warning(f"Invalid voltage readout: {val}")
        # 过滤电压大于30V的数据，使用上一帧数据
        if val > 30:
            return self._last_readout_v
        if val < 0.01 or val == 2.000:
            # logger.warning(f"Invalid voltage readout: {val}")
            fixed_val = self._last_readout_v
            self._last_readout_v = val
            return fixed_val
        self._last_readout_v = val
        return val

    def get_output_current(self) -> float:
        """
        Get the output current of the WPX .
        """
        self._reader.clear()
        self._ser.write(b"MEAS:CURR?")
        val = float(self._read_until_ok())
        # if val > 10:
        #     logger.warning(f"Invalid current readout: {val}")
        # 过滤电流大于3A的数据，使用上一帧数据
        if val > 3:
            return self._last_readout_i
        if val < 0.01:
            fixed_val = self._last_readout_i
            self._last_readout_i = val
            return fixed_val
        self._last_readout_i = val
        return val

    def get_output_power(self) -> float:
        """
        Get the output power of the WPX .
        """
        self._reader.clear()
        # self._ser.write(b"MEAS:POW?")
        return float(self._read_until_ok())

    def set_voltage(self, voltage_set: float):
        """
        Set the output voltage of the WPX .

        Args:
            voltage_set (float): The voltage to be set, in V, steps of 0.01V.
        """
        self._ser.write(f"VOLT {voltage_set:.2f}\r\n".encode())

    def set_current(self, current_set: float):
        """
        Set the output current of the WPX .

        Args:
            current_set (float): The current to be set, in A, steps of 0.01A.
        """
        self._ser.write(f"CURR {current_set:.2f}\r\n".encode())

    def get_set_voltage(self) -> float:
        """
        Get the set voltage of the WPX .
        """
        self._ser.write(b"get vset\r\n")
        return float(self._read_until_ok())

    def get_set_current(self) -> float:
        """
        Get the set current of the WPX .
        """
        self._ser.write(b"get iset\r\n")
        return float(self._read_until_ok())

    def set_max_power(self, power_set: float):
        """
        Set the maximum power of the WPX .
        """
        self._ser.write(f"setpower {power_set:.0f}\r\n".encode())

    def set_output(self, state: bool):
        """
        Set the output state of the WPX .
        """
        self._ser.write(b"OUTP ON\r\n" if state else b"OUTP OFF\r\n")

    def set_short_protect(self, state: bool):
        """
        Set the short protection state of the WPX .
        """
        self._ser.write(b"setSFB 1\r\n" if state else b"setSFB 0\r\n")

    def set_key_lock(self, state: bool):
        """
        Set the key lock state of the WPX .
        """
        self._ser.write(b"lockkey 1\r\n" if state else b"lockkey 0\r\n")

    def calibrate_output(self):
        """
        Calibrate the output of the WPX . (Use 7V)
        """
        self._ser.write(b"calibus 1\r\n")

    def calibrate_input(self):
        """
        Calibrate the input of the WPX . (Use 20V)
        """
        self._ser.write(b"calibus 2\r\n")

    def request_message(self, interval: int = 100):
        """
        Request message from the WPX .
        """
        assert 0 <= interval <= 65535
        try:
            if self._ser and self._ser.is_open:
                self._ser.write(f"getmsg {interval}\r\n".encode())
        except Exception as e:
            logger.warning(f"Failed to write request_message: {e}")
            # 向上抛出异常以便上层检测设备断开
            raise e

    def parse_message(self):
        """
        Parse message from the WPX .
        """
        # 如果正在固件升级，直接返回None，不读取串口
        if hasattr(self, '_firmware_update_blocked') and self._firmware_update_blocked:
            return None
            
        # 调试：初始化计数器
        if not hasattr(self, '_parse_call_count'):
            self._parse_call_count = 0
        self._parse_call_count += 1
        
        # 调试：记录parse_message调用时机和WPX协议状态
        # if self._parse_call_count % 100 == 1:
        #     wpx_has_data = hasattr(self.wpx_protocol, '_data_update_logged') if self.wpx_protocol else False
        #     logger.info(f"parse_message called #{self._parse_call_count}: wpx_protocol={self.wpx_protocol is not None}, wpx_has_data_flag={wpx_has_data}")
        
        # 如果使用WPX协议，优先从WPX协议获取数据，不依赖binary_queue
        if self.use_wpx_protocol and self.wpx_protocol:
            # 跳过binary_queue检查，直接进入WPX协议数据处理
            pass
        else:
            # 原始协议：检查binary_queue
            if self._reader.binary_queue.empty():
                # 每1000次记录一次队列空状态
                if self._parse_call_count % 1000 == 1:
                    logger.info(f"Parse message #{self._parse_call_count}: binary_queue is empty")
                return None
        
        # 如果使用WPX协议，直接从WPX协议获取数据，否则从binary_queue获取
        if self.use_wpx_protocol and self.wpx_protocol:
            # WPX协议模式：不从binary_queue获取数据，直接处理WPX协议数据
            data = None  # 不使用binary_queue的数据
        else:
            # 原始协议模式：从binary_queue获取数据
            data = self._reader.binary_queue.get(block=False)
            
            # 调试：记录数据获取情况
            if self._parse_call_count % 1000 == 1:
                logger.info(f"Parse message #{self._parse_call_count}: got data={data is not None}, data_len={len(data) if data else 0}")

            if not data:
                return None
                
            # 调试：记录一次数据以便分析
            if not hasattr(self, '_parse_debug_logged'):
                logger.debug(f"Parse message - data length: {len(data) if data else 0}")
                if data:
                    logger.debug(f"Parse message - data content: {data.hex() if isinstance(data, bytes) else data}")
                self._parse_debug_logged = True
            
            # 如果 data 为字符串则转换为二进制数据
            if isinstance(data, str):
                data = bytes.fromhex(data)

        # WPX协议数据处理：直接从wpx_protocol获取已解析的数据
        # 检查wpx_protocol是否有最新数据
        if hasattr(self.wpx_protocol, 'vout_data') and hasattr(self.wpx_protocol, 'isence_data'):
            # 从wpx_protocol获取已解析的数据
            vout_raw = getattr(self.wpx_protocol, 'vout_data', 0)
            isence_raw = getattr(self.wpx_protocol, 'isence_data', 0)
            
            # 调试：记录一次数据获取过程
            if not hasattr(self, '_data_access_logged'):
                logger.info(f"Accessing WPX protocol data: vout_raw={vout_raw}, isence_raw={isence_raw}")
                logger.info(f"WPX protocol flags: _data_update_logged={hasattr(self.wpx_protocol, '_data_update_logged')}")
                self._data_access_logged = True
            
            # 调试：每1000次记录一次数据状态
            if self._parse_call_count % 1000 == 1:
                logger.info(f"WPX protocol data access #{self._parse_call_count}: vout_raw={vout_raw}, isence_raw={isence_raw}")
            
            # 调试：记录WPX协议状态检查
            has_data_flag = hasattr(self.wpx_protocol, '_data_update_logged')
            if self._parse_call_count % 100 == 1:
                logger.info(f"WPX protocol status check #{self._parse_call_count}: has_data_flag={has_data_flag}, vout_raw={vout_raw}, isence_raw={isence_raw}")
            
            # 简化逻辑：一旦WPX协议收到过数据就开始返回数据
            if has_data_flag:
                # WPX协议已经收到并解析过数据，直接返回当前数据
                if not hasattr(self, '_stream_established_logged'):
                    logger.info(f"Data stream established, current values: vout={vout_raw}, isence={isence_raw}")
                    self._stream_established_logged = True
            else:
                # WPX协议还没有收到数据，返回None
                if self._parse_call_count % 100 == 1:  # 每100次记录一次等待状态
                    logger.info(f"Waiting for WPX protocol data... call #{self._parse_call_count}, has_data_flag={has_data_flag}")
                return None
            
            # 转换单位
            vout_val = vout_raw / 1000
            iout_val = isence_raw / 1000
            
            # 过滤异常数据
            if vout_val > 30:
                vout_val = self._last_readout_v
            if iout_val > 3:
                iout_val = self._last_readout_i
                
            # 更新上一帧数据
            self._last_readout_v = vout_val
            self._last_readout_i = iout_val
            
            result = {
                "vout": vout_val,
                "iout": iout_val,
                "state": 1
            }
            
            # 调试：记录第一次成功返回数据
            if not hasattr(self, '_first_return_logged'):
                logger.info(f"First parse_message return: V={vout_val:.3f}V, I={iout_val:.3f}A")
                self._first_return_logged = True
            
            # 调试：每100次记录一次返回的数据
            if self._parse_call_count % 100 == 1:
                logger.info(f"parse_message returning data #{self._parse_call_count}: V={vout_val:.3f}V, I={iout_val:.3f}A")
            
            return result
        
        # 如果wpx_protocol没有数据，尝试原始解析方法作为备用
        if not data or len(data) < 7 + 6:
            return None

        payload = data[7:7+6]
        # 解析字段：3个uint16_t (各占2字节)
        vrect, vout, isence = struct.unpack("<3H", payload)

        # 转换单位
        vrect_val = vrect / 1000
        vout_val = vout / 1000
        iout_val = isence / 1000
        
        # 过滤电压大于30V或电流大于3A的数据，使用上一帧数据
        if vrect_val > 30:
            vrect_val = self._last_readout_v
        if vout_val > 30:
            vout_val = self._last_readout_v
        if iout_val > 3:
            iout_val = self._last_readout_i
            
        # 更新上一帧数据
        self._last_readout_v = vrect_val
        self._last_readout_i = iout_val
        
        return {
            "vout": vrect_val,
            "iout": iout_val,
            "state": 1
        }

    def _send_wpx_data(self, data: bytes):
        """Send WPX protocol data via serial"""
        try:
            if self._ser and self._ser.is_open:
                self._ser.write(data)
                logger.debug(f"Sent WPX data: {data.hex()}")
            else:
                raise Exception("Serial port is not available for writing")
        except Exception as e:
            logger.warning(f"Failed to send WPX data: {e}")
            # 向上抛出异常以便上层检测设备断开
            raise e

    # WPX Protocol Methods
    def wpx_check_sn(self):
        """Check device serial number using WPX protocol"""
        if self.wpx_protocol:
            self.wpx_protocol.check_sn_cmd()

    def wpx_check_version(self):
        """Check device version using WPX protocol"""
        if self.wpx_protocol:
            self.wpx_protocol.check_version_cmd()

    def wpx_enter_bootloader(self):
        """Enter bootloader mode using WPX protocol"""
        if self.wpx_protocol:
            self.wpx_protocol.enter_bootloader_cmd()

    def wpx_factory_reset(self):
        """Factory reset device using WPX protocol"""
        if self.wpx_protocol:
            self.wpx_protocol.factory_reset_cmd()

    def wpx_start_protocol(self):
        """Start WPX protocol communication"""
        if self.wpx_protocol:
            self.wpx_protocol.start_protocol_cmd()

    def wpx_get_data(self):
        """Get latest WPX protocol data"""
        if self.wpx_protocol:
            return self.wpx_protocol.get_default_data()
        return None

    def wpx_get_version_info(self):
        """Get WPX version information"""
        if self.wpx_protocol:
            return self.wpx_protocol.get_version_data()
        return None

    def wpx_get_sn_info(self):
        """Get WPX serial number information"""
        if self.wpx_protocol:
            return self.wpx_protocol.get_sn_data()
        return None

    def wpx_set_output_callback(self, callback):
        """Set callback for WPX protocol output (for GUI display)"""
        if self.wpx_protocol:
            self.wpx_protocol.set_output_callback(callback)

    def wpx_set_updating(self, state: bool):
        """Set WPX protocol updating state"""
        if self.wpx_protocol:
            self.wpx_protocol.is_updating = state


    @property
    def a(self) -> Self:
        """
        Compatible with PDPocketQThread
        """
        return self


if __name__ == "__main__":

    def timed_print(func, *args, **kwargs):
        t0 = time.perf_counter()
        res = func(*args, **kwargs)
        t1 = time.perf_counter() - t0
        logger.info(f"Call {func.__name__}(), Return: {res}, Cost: {t1:.6f}s")

    device = PDPocket()
    timed_print(device.set_output, False)
    time.sleep(0.1)
    timed_print(device.get_output_voltage)
    timed_print(device.get_output_current)
    timed_print(device.get_output_power)
    timed_print(device.set_output, True)
    time.sleep(0.1)
    timed_print(device.get_output_voltage)
    timed_print(device.get_output_current)
    timed_print(device.get_output_power)
    # timed_print(device.set_voltage, 12)
    # timed_print(device.set_current, 1)
    # timed_print(device.set_max_power, 100)
    # timed_print(device.set_output_state, True)
    # time.sleep(1)
    # timed_print(device.get_output_voltage)
    # timed_print(device.get_output_current)
    # timed_print(device.get_output_power)
    # timed_print(device.set_output_state, False)
